#!/usr/bin/env python3
"""
Advanced Discord Commands for Trading Bot Phase 3
Implements /positions, /pnl, /close, /history commands
Real-time status display and position management
"""

import logging
import discord
from discord.ext import commands, tasks
from discord import app_commands
from typing import Optional
from datetime import datetime, timezone, timedelta
import asyncio

from services.trading.position_manager import position_manager
from services.trading.trading_service import BinanceFuturesTrading
from services.trading.order_tracker import order_tracker
from services.data.database import trading_db
from services.core.symbol_service import smart_normalize_symbol

logger = logging.getLogger(__name__)

class AdvancedTradingCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.trade_channel_name = "trade"
        self.trading_service = None
        self.status_message = None
        self.status_channel_id = None

    async def cog_load(self):
        """Initialize trading service and start status update task"""
        try:
            self.trading_service = BinanceFuturesTrading()
            self.update_status_message.start()
            logger.info("✅ Advanced trading commands initialized with status updates")
        except Exception as e:
            logger.error(f"❌ Failed to initialize advanced trading commands: {e}")

    async def setup_auto_status(self):
        """Setup automatic status message in trade channel"""
        try:
            # Find trade channel
            trade_channel = None
            for guild in self.bot.guilds:
                for channel in guild.text_channels:
                    if channel.name == self.trade_channel_name:
                        trade_channel = channel
                        break
                if trade_channel:
                    break

            if not trade_channel:
                logger.warning(f"⚠️ Trade channel '{self.trade_channel_name}' not found")
                return

            # Get admin user ID from database
            users = trading_db.get_users()
            if not users:
                logger.warning("⚠️ No users found in database for auto status")
                return

            admin_user_id = users[0]['user_id']

            # Create status embed
            embed = await self._create_status_embed(admin_user_id)

            # Send and pin the message
            message = await trade_channel.send(embed=embed)
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = trade_channel.id

            logger.info(f"✅ Auto status message created and pinned in #{trade_channel.name}")

        except Exception as e:
            logger.error(f"❌ Error setting up auto status: {e}")

    def cog_unload(self):
        """Stop status update task when cog unloads"""
        if self.update_status_message.is_running():
            self.update_status_message.cancel()

    def _check_trade_channel(self, interaction: discord.Interaction) -> bool:
        """Check if command is used in trade channel"""
        return interaction.channel.name == self.trade_channel_name



    @tasks.loop(seconds=60)
    async def update_status_message(self):
        """Update pinned status message every 60 seconds with improved error handling"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.status_message or not self.status_channel_id:
                    logger.debug("No status message to update")
                    return

                channel = self.bot.get_channel(self.status_channel_id)
                if not channel:
                    logger.warning(f"Status channel {self.status_channel_id} not found")
                    return

                # Get admin user ID from config or first user in database
                users = trading_db.get_users()
                if not users:
                    logger.debug("No users found for status update")
                    return

                admin_user_id = users[0]['user_id']

                # Create status embed with error handling
                try:
                    embed = await self._create_status_embed(admin_user_id)
                except Exception as embed_error:
                    logger.error(f"Error creating status embed: {embed_error}")
                    # Create fallback embed
                    embed = discord.Embed(
                        title="📊 Trading Status Dashboard",
                        description="⚠️ Error loading status data",
                        color=0xffa500,
                        timestamp=datetime.now(timezone.utc)
                    )
                    embed.set_footer(text="Status update failed - retrying next cycle")

                # Update the pinned message
                await self.status_message.edit(embed=embed)
                logger.debug("Status message updated successfully")
                return  # Success, exit the retry loop

            except discord.errors.NotFound as e:
                logger.warning(f"Status message not found, stopping updates: {e}")
                self.status_message = None
                self.status_channel_id = None
                return
            except discord.errors.Forbidden as e:
                logger.error(f"No permission to update status message: {e}")
                return
            except discord.errors.ConnectionClosed as e:
                retry_count += 1
                logger.warning(f"Discord connection closed (attempt {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    await asyncio.sleep(2 * retry_count)
                else:
                    logger.error(f"❌ Failed to update status message after {max_retries} attempts: {e}")
            except discord.errors.HTTPException as e:
                retry_count += 1
                logger.warning(f"Discord HTTP error (attempt {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    await asyncio.sleep(2 * retry_count)
                else:
                    logger.error(f"❌ Failed to update status message after {max_retries} attempts: {e}")
            except Exception as e:
                logger.error(f"❌ Unexpected error updating status message: {e}")
                return  # Don't retry for other types of errors

    @update_status_message.before_loop
    async def before_update_status_message(self):
        """Wait for bot to be ready before starting status updates"""
        await self.bot.wait_until_ready()

        # Setup auto status message if not already exists
        if not self.status_message:
            await asyncio.sleep(5)  # Wait a bit for bot to fully initialize
            await self.setup_auto_status()

    async def _create_status_embed(self, user_id: str) -> discord.Embed:
        """Create comprehensive status embed"""
        try:
            # Get account balance with improved error handling
            balance_info = {}

            if self.trading_service:
                try:
                    balance_info = self.trading_service.get_account_balance(use_cache=True)
                except Exception as e:
                    logger.error(f"Error getting balance: {e}")
                    balance_info = {'success': False, 'error': str(e), 'fallback': True}

            # Get open positions
            open_positions = position_manager.get_user_positions(user_id, status='open')

            # Get open orders for this user only
            open_orders = [o for o in trading_db.get_orders(user_id=user_id)
                          if o['status'] in ['pending', 'partially_filled'] and o.get('symbol')]

            # Calculate totals
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in open_positions)
            total_margin_used = sum(p.get('margin_used', 0) for p in open_positions)

            # Determine embed color based on connection and P&L
            if balance_info.get('fallback') or not balance_info.get('success'):
                embed_color = 0xffa500  # Orange for connection issues
            elif total_unrealized_pnl >= 0:
                embed_color = 0x00ff88  # Green for profit
            else:
                embed_color = 0xff4444  # Red for loss

            # Create embed
            embed = discord.Embed(
                title="📊 Trading Status Dashboard",
                color=embed_color,
                timestamp=datetime.now(timezone.utc)
            )

            # Account Status with connection indicator
            if balance_info.get('success'):
                total_balance = balance_info.get('total_usdt', 0)
                free_balance = balance_info.get('free_usdt', 0)
                available_balance = free_balance - total_margin_used

                status_indicator = "🟡" if balance_info.get('stale') else "🟢"
                data_source = " (cached)" if balance_info.get('stale') else ""
            else:
                total_balance = 0
                free_balance = 0
                available_balance = 0
                status_indicator = "🔴"
                data_source = " (unavailable)"

            embed.add_field(
                name=f"💰 Account Status {status_indicator}",
                value=f"**Total Balance:** ${total_balance:,.2f}{data_source}\n"
                      f"**Free Balance:** ${free_balance:,.2f}\n"
                      f"**Available:** ${available_balance:,.2f}\n"
                      f"**Margin Used:** ${total_margin_used:,.2f}",
                inline=True
            )

            # P&L Summary
            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="📈 P&L Summary",
                value=f"**Unrealized P&L:** {pnl_color} ${total_unrealized_pnl:,.2f}\n"
                      f"**Open Positions:** {len(open_positions)}\n"
                      f"**Pending Orders:** {len(open_orders)}",
                inline=True
            )

            # Position Summary
            if open_positions:
                position_text = ""
                for i, pos in enumerate(open_positions[:5]):  # Show max 5
                    pnl = pos.get('unrealized_pnl', 0)
                    pnl_emoji = "🟢" if pnl >= 0 else "🔴"
                    position_text += f"**{pos.get('symbol')}** {pos.get('position_side')} {pnl_emoji} ${pnl:,.2f}\n"

                if len(open_positions) > 5:
                    position_text += f"... and {len(open_positions) - 5} more"

                embed.add_field(
                    name="📊 Open Positions",
                    value=position_text or "No open positions",
                    inline=False
                )

            # Orders Summary
            if open_orders:
                order_text = ""
                for i, order in enumerate(open_orders[:5]):  # Show max 5
                    order_text += f"**{order.get('symbol')}** {order.get('side')} @ ${order.get('price', 0):,.2f}\n"

                if len(open_orders) > 5:
                    order_text += f"... and {len(open_orders) - 5} more"

                embed.add_field(
                    name="📋 Pending Orders",
                    value=order_text or "No pending orders",
                    inline=False
                )

            # Connection status footer
            footer_text = "Auto-updated every 60 seconds | Use /status to pin this message"
            if balance_info.get('fallback'):
                footer_text = "⚠️ API Connection Issues - Using fallback data | " + footer_text
            elif balance_info.get('stale'):
                footer_text = "⚠️ Using cached data due to API issues | " + footer_text

            embed.set_footer(text=footer_text)
            return embed

        except Exception as e:
            logger.error(f"❌ Error creating status embed: {e}")
            return discord.Embed(
                title="❌ Status Error",
                description=f"Error creating status: {str(e)}",
                color=0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

    @app_commands.command(name="status", description="Create/update pinned status message")
    async def create_status_message(self, interaction: discord.Interaction):
        """Create or update the pinned status message"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Check if auto status already exists
            if self.status_message and self.status_channel_id == interaction.channel.id:
                # Update existing message
                embed = await self._create_status_embed(user_id)
                
                # Tạo view với nút Sync
                view = discord.ui.View()
                sync_button = discord.ui.Button(style=discord.ButtonStyle.primary, label="Sync", emoji="🔄")
                
                async def sync_callback(interaction: discord.Interaction):
                    await interaction.response.defer()
                    try:
                        # Lấy user ID
                        user_id = str(interaction.user.id)
                        
                        # Kích hoạt đối chiếu ngay lập tức
                        result = await order_tracker.force_reconcile_orders(user_id)
                        
                        if result['success']:
                            # Cập nhật status dashboard sau khi đồng bộ
                            new_embed = await self._create_status_embed(user_id)
                            await interaction.message.edit(embed=new_embed)
                            
                            # Thông báo thành công
                            sync_embed = discord.Embed(
                                title="✅ Đồng bộ hóa thành công",
                                description="Đã đồng bộ hóa trạng thái lệnh với Binance",
                                color=0x00ff88,
                                timestamp=datetime.now(timezone.utc)
                            )
                            
                            sync_embed.add_field(
                                name="Số lệnh đã kiểm tra",
                                value=f"{result['total_orders']}",
                                inline=True
                            )
                            
                            sync_embed.add_field(
                                name="Số lệnh đã đồng bộ",
                                value=f"{result['reconciled_count']}",
                                inline=True
                            )
                            
                            await interaction.followup.send(embed=sync_embed, ephemeral=True)
                        else:
                            # Thông báo lỗi
                            await interaction.followup.send(f"❌ Đồng bộ thất bại: {result['error']}", ephemeral=True)
                            
                    except Exception as e:
                        logger.error(f"❌ Lỗi khi đồng bộ hóa: {e}")
                        await interaction.followup.send(f"❌ Lỗi: {str(e)}", ephemeral=True)
                
                sync_button.callback = sync_callback
                view.add_item(sync_button)
                
                await self.status_message.edit(embed=embed, view=view)
                await interaction.followup.send("✅ Status message updated!", ephemeral=True)
                logger.info(f"✅ Status message updated by {interaction.user}")
                return

            # Create new status embed
            embed = await self._create_status_embed(user_id)

            # Tạo view với nút Sync
            view = discord.ui.View()
            sync_button = discord.ui.Button(style=discord.ButtonStyle.primary, label="Sync", emoji="🔄")
            
            async def sync_callback(interaction: discord.Interaction):
                await interaction.response.defer()
                try:
                    # Lấy user ID
                    user_id = str(interaction.user.id)
                    
                    # Kích hoạt đối chiếu ngay lập tức
                    result = await order_tracker.force_reconcile_orders(user_id)
                    
                    if result['success']:
                        # Cập nhật status dashboard sau khi đồng bộ
                        new_embed = await self._create_status_embed(user_id)
                        await interaction.message.edit(embed=new_embed)
                        
                        # Thông báo thành công
                        sync_embed = discord.Embed(
                            title="✅ Đồng bộ hóa thành công",
                            description="Đã đồng bộ hóa trạng thái lệnh với Binance",
                            color=0x00ff88,
                            timestamp=datetime.now(timezone.utc)
                        )
                        
                        sync_embed.add_field(
                            name="Số lệnh đã kiểm tra",
                            value=f"{result['total_orders']}",
                            inline=True
                        )
                        
                        sync_embed.add_field(
                            name="Số lệnh đã đồng bộ",
                            value=f"{result['reconciled_count']}",
                            inline=True
                        )
                        
                        await interaction.followup.send(embed=sync_embed, ephemeral=True)
                    else:
                        # Thông báo lỗi
                        await interaction.followup.send(f"❌ Đồng bộ thất bại: {result['error']}", ephemeral=True)
                        
                except Exception as e:
                    logger.error(f"❌ Lỗi khi đồng bộ hóa: {e}")
                    await interaction.followup.send(f"❌ Lỗi: {str(e)}", ephemeral=True)
            
            sync_button.callback = sync_callback
            view.add_item(sync_button)
            
            # Gửi message với nút Sync
            message = await interaction.followup.send(embed=embed, view=view)

            # Pin the message
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = interaction.channel.id

            logger.info(f"✅ Status message created and pinned by {interaction.user}")

        except Exception as e:
            logger.error(f"❌ Error creating status message: {e}")
            await interaction.followup.send(f"❌ Error creating status message: {str(e)}")

    @app_commands.command(name="statusinfo", description="Check auto status system information")
    async def status_info(self, interaction: discord.Interaction):
        """Check auto status system information"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        embed = discord.Embed(
            title="📊 Auto Status System Info",
            color=0x3498db,
            timestamp=datetime.now(timezone.utc)
        )

        # Status message info
        if self.status_message:
            embed.add_field(
                name="📌 Status Message",
                value=f"✅ Active\nMessage ID: `{self.status_message.id}`\nChannel: <#{self.status_channel_id}>",
                inline=False
            )
        else:
            embed.add_field(
                name="📌 Status Message",
                value="❌ Not active",
                inline=False
            )

        # Update task info
        if self.update_status_message.is_running():
            embed.add_field(
                name="🔄 Auto Update Task",
                value="✅ Running (60s interval)",
                inline=True
            )
        else:
            embed.add_field(
                name="🔄 Auto Update Task",
                value="❌ Not running",
                inline=True
            )

        # Trading service info with connection status
        if self.trading_service:
            try:
                connection_status = self.trading_service.get_connection_status()
                health_indicator = "✅" if connection_status['healthy'] else "⚠️"
                last_fetch = connection_status.get('last_fetch', 'Never')
                cache_size = connection_status.get('cache_size', 0)

                embed.add_field(
                    name="⚡ Trading Service",
                    value=f"{health_indicator} Connected\n"
                          f"Cache: {cache_size} items\n"
                          f"Last fetch: {last_fetch[:19] if last_fetch != 'Never' else 'Never'}",
                    inline=True
                )
            except Exception as e:
                embed.add_field(
                    name="⚡ Trading Service",
                    value=f"⚠️ Error: {str(e)[:30]}...",
                    inline=True
                )
        else:
            embed.add_field(
                name="⚡ Trading Service",
                value="❌ Not available",
                inline=True
            )

        # Database info
        try:
            users = trading_db.get_users()
            embed.add_field(
                name="🗄️ Database",
                value=f"✅ Connected\nUsers: {len(users)}",
                inline=True
            )
        except Exception as e:
            embed.add_field(
                name="🗄️ Database",
                value=f"❌ Error: {str(e)[:50]}...",
                inline=True
            )

        embed.set_footer(text="Use /status to create/update status message")
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="refresh", description="Force refresh balance data and clear cache")
    async def refresh_balance(self, interaction: discord.Interaction):
        """Force refresh balance data"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Clear cache and force fresh fetch
            self.trading_service.balance_cache.clear()
            balance_info = self.trading_service.get_account_balance(use_cache=False)

            embed = discord.Embed(
                title="🔄 Balance Refresh",
                color=0x00ff88 if balance_info.get('success') else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            if balance_info.get('success'):
                embed.add_field(
                    name="✅ Success",
                    value=f"**Total Balance:** ${balance_info.get('total_usdt', 0):,.2f}\n"
                          f"**Free Balance:** ${balance_info.get('free_usdt', 0):,.2f}\n"
                          f"**Timestamp:** {balance_info.get('timestamp', 'N/A')[:19]}",
                    inline=False
                )
            else:
                embed.add_field(
                    name="❌ Failed",
                    value=f"**Error:** {balance_info.get('error', 'Unknown error')}",
                    inline=False
                )

            # Update status message if it exists
            if self.status_message:
                try:
                    user_id = str(interaction.user.id)
                    status_embed = await self._create_status_embed(user_id)
                    await self.status_message.edit(embed=status_embed)
                    embed.add_field(
                        name="📊 Status Updated",
                        value="Dashboard has been refreshed",
                        inline=False
                    )
                except Exception as e:
                    embed.add_field(
                        name="⚠️ Status Update",
                        value=f"Failed to update dashboard: {str(e)[:50]}...",
                        inline=False
                    )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Error refreshing balance: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="tp", description="Set take profit for position")
    @app_commands.describe(
        symbol="Symbol (e.g., btc, eth, BTCUSDT) - auto adds USDT",
        side="Position side (LONG/SHORT)",
        price="Take profit price",
        percentage="Percentage of position to close (25/50/75/100)"
    )
    async def set_take_profit(self, interaction: discord.Interaction,
                             symbol: str, side: str, price: float,
                             percentage: Optional[int] = 100):
        """Set take profit for a position"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        if percentage not in [25, 50, 75, 100]:
            await interaction.response.send_message("❌ Percentage must be 25, 50, 75, or 100", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)
            symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Get position
            position = position_manager.get_position(user_id, symbol, side)
            if not position or position['status'] != 'open':
                await interaction.followup.send(f"❌ No open {side} position found for {symbol}")
                return

            # Calculate amount to close
            position_amount = abs(position['amount'])
            close_amount = position_amount * (percentage / 100)

            # Determine order side (opposite of position)
            order_side = 'sell' if side == 'LONG' else 'buy'

            # Place take profit order
            result = self.trading_service.place_take_profit_order(
                symbol=symbol,
                side=order_side,
                amount=close_amount,
                stop_price=price,
                position_side=side,
                reduce_only=True
            )

            if result['success']:
                embed = discord.Embed(
                    title="✅ Take Profit Set",
                    color=0x00ff88,
                    timestamp=datetime.now(timezone.utc)
                )

                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Position", value=side, inline=True)
                embed.add_field(name="TP Price", value=f"${price:,.2f}", inline=True)
                embed.add_field(name="Amount", value=f"{close_amount:.6f} ({percentage}%)", inline=True)
                embed.add_field(name="Order ID", value=f"`{result['order_id']}`", inline=True)

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Failed to set take profit: {result['error']}")

        except Exception as e:
            logger.error(f"❌ Error setting take profit: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="sl", description="Set stop loss for position")
    @app_commands.describe(
        symbol="Symbol (e.g., btc, eth, BTCUSDT) - auto adds USDT",
        side="Position side (LONG/SHORT)",
        price="Stop loss price",
        percentage="Percentage of position to close (25/50/75/100)"
    )
    async def set_stop_loss(self, interaction: discord.Interaction,
                           symbol: str, side: str, price: float,
                           percentage: Optional[int] = 100):
        """Set stop loss for a position"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        if percentage not in [25, 50, 75, 100]:
            await interaction.response.send_message("❌ Percentage must be 25, 50, 75, or 100", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)
            symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Get position
            position = position_manager.get_position(user_id, symbol, side)
            if not position or position['status'] != 'open':
                await interaction.followup.send(f"❌ No open {side} position found for {symbol}")
                return

            # Calculate amount to close
            position_amount = abs(position['amount'])
            close_amount = position_amount * (percentage / 100)

            # Determine order side (opposite of position)
            order_side = 'sell' if side == 'LONG' else 'buy'

            # Place stop loss order
            result = self.trading_service.place_stop_loss_order(
                symbol=symbol,
                side=order_side,
                amount=close_amount,
                stop_price=price,
                position_side=side,
                reduce_only=True
            )

            if result['success']:
                embed = discord.Embed(
                    title="✅ Stop Loss Set",
                    color=0xff6b35,
                    timestamp=datetime.now(timezone.utc)
                )

                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Position", value=side, inline=True)
                embed.add_field(name="SL Price", value=f"${price:,.2f}", inline=True)
                embed.add_field(name="Amount", value=f"{close_amount:.6f} ({percentage}%)", inline=True)
                embed.add_field(name="Order ID", value=f"`{result['order_id']}`", inline=True)

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Failed to set stop loss: {result['error']}")

        except Exception as e:
            logger.error(f"❌ Error setting stop loss: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="sync", description="Đồng bộ hóa trạng thái lệnh với Binance")
    async def sync_orders(self, interaction: discord.Interaction):
        """Kích hoạt đối chiếu ngay lập tức để đồng bộ trạng thái lệnh với Binance"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh này chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return
            
        await interaction.response.defer()
        
        try:
            # Lấy user ID
            user_id = str(interaction.user.id)
            
            # Kích hoạt đối chiếu ngay lập tức
            result = await order_tracker.force_reconcile_orders(user_id)
            
            if result['success']:
                embed = discord.Embed(
                    title="✅ Đồng bộ hóa thành công",
                    description="Đã đồng bộ hóa trạng thái lệnh với Binance",
                    color=0x00ff88,
                    timestamp=datetime.now(timezone.utc)
                )
                
                embed.add_field(
                    name="Số lệnh đã kiểm tra",
                    value=f"{result['total_orders']}",
                    inline=True
                )
                
                embed.add_field(
                    name="Số lệnh đã đồng bộ",
                    value=f"{result['reconciled_count']}",
                    inline=True
                )
                
                embed.add_field(
                    name="Thời gian",
                    value=f"{result['timestamp'][:19]}",
                    inline=True
                )
                
                embed.set_footer(text="Sử dụng lệnh này khi nghi ngờ có sự không đồng bộ giữa bot và Binance")
                
                await interaction.followup.send(embed=embed)
            else:
                embed = discord.Embed(
                    title="❌ Đồng bộ hóa thất bại",
                    description=f"Lỗi: {result['error']}",
                    color=0xff4444,
                    timestamp=datetime.now(timezone.utc)
                )
                
                if 'last_reconciliation' in result:
                    embed.add_field(
                        name="Lần đồng bộ cuối",
                        value=f"{result['last_reconciliation'][:19]}",
                        inline=False
                    )
                    embed.set_footer(text="Vui lòng đợi ít nhất 10 giây giữa các lần đồng bộ")
                
                await interaction.followup.send(embed=embed)
        
        except Exception as e:
            logger.error(f"❌ Lỗi khi đồng bộ hóa: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")
    
    @app_commands.command(name="closeall", description="Close all open positions")
    @app_commands.describe(
        confirm="Type 'yes' to confirm closing all positions"
    )
    async def close_all_positions(self, interaction: discord.Interaction,
                                 confirm: Optional[str] = None):
        """Close all open positions"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        if confirm != "yes":
            await interaction.response.send_message(
                "⚠️ This will close ALL positions! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Get all open positions
            open_positions = position_manager.get_user_positions(user_id, status='open')

            if not open_positions:
                await interaction.followup.send("❌ No open positions to close")
                return

            closed_count = 0
            failed_count = 0
            total_pnl = 0

            for position in open_positions:
                try:
                    symbol = position['symbol']
                    position_side = position['position_side']
                    amount = abs(position['amount'])

                    # Determine order side (opposite of position)
                    order_side = 'sell' if position_side == 'LONG' else 'buy'

                    # Place market order to close position
                    result = self.trading_service.place_market_order(
                        symbol=symbol,
                        side=order_side,
                        amount=amount,
                        position_side=position_side
                    )

                    if result['success']:
                        closed_count += 1
                        total_pnl += position.get('unrealized_pnl', 0)
                    else:
                        failed_count += 1
                        logger.error(f"Failed to close {symbol} {position_side}: {result.get('error')}")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error closing position {position.get('symbol')}: {e}")

            # Create result embed
            embed = discord.Embed(
                title="🔒 Close All Positions Result",
                color=0x00ff88 if failed_count == 0 else 0xff6b35,
                timestamp=datetime.now(timezone.utc)
            )

            embed.add_field(name="Closed", value=str(closed_count), inline=True)
            embed.add_field(name="Failed", value=str(failed_count), inline=True)
            embed.add_field(name="Total P&L", value=f"${total_pnl:,.2f}", inline=True)

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Error closing all positions: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="closeside", description="Close all positions on one side (LONG or SHORT)")
    @app_commands.describe(
        side="Side to close (LONG/SHORT)",
        confirm="Type 'yes' to confirm"
    )
    async def close_side_positions(self, interaction: discord.Interaction,
                                  side: str, confirm: Optional[str] = None):
        """Close all positions on one side"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        side = side.upper()
        if side not in ['LONG', 'SHORT']:
            await interaction.response.send_message("❌ Side must be LONG or SHORT", ephemeral=True)
            return

        if confirm != "yes":
            await interaction.response.send_message(
                f"⚠️ This will close ALL {side} positions! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Get open positions for the specified side
            all_positions = position_manager.get_user_positions(user_id, status='open')
            side_positions = [p for p in all_positions if p['position_side'] == side]

            if not side_positions:
                await interaction.followup.send(f"❌ No open {side} positions to close")
                return

            closed_count = 0
            failed_count = 0
            total_pnl = 0

            for position in side_positions:
                try:
                    symbol = position['symbol']
                    amount = abs(position['amount'])

                    # Determine order side (opposite of position)
                    order_side = 'sell' if side == 'LONG' else 'buy'

                    # Place market order to close position
                    result = self.trading_service.place_market_order(
                        symbol=symbol,
                        side=order_side,
                        amount=amount,
                        position_side=side
                    )

                    if result['success']:
                        closed_count += 1
                        total_pnl += position.get('unrealized_pnl', 0)
                    else:
                        failed_count += 1
                        logger.error(f"Failed to close {symbol} {side}: {result.get('error')}")

                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error closing position {position.get('symbol')}: {e}")

            # Create result embed
            embed = discord.Embed(
                title=f"🔒 Close {side} Positions Result",
                color=0x00ff88 if failed_count == 0 else 0xff6b35,
                timestamp=datetime.now(timezone.utc)
            )

            embed.add_field(name="Closed", value=str(closed_count), inline=True)
            embed.add_field(name="Failed", value=str(failed_count), inline=True)
            embed.add_field(name="Total P&L", value=f"${total_pnl:,.2f}", inline=True)

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Error closing {side} positions: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="closepos", description="Close specific position with percentage")
    @app_commands.describe(
        symbol="Symbol (e.g., btc, eth, BTCUSDT) - auto adds USDT",
        side="Position side (LONG/SHORT)",
        percentage="Percentage to close (25/50/75/100)",
        confirm="Type 'yes' to confirm"
    )
    async def close_position_partial(self, interaction: discord.Interaction,
                                   symbol: str, side: str, percentage: int,
                                   confirm: Optional[str] = None):
        """Close position with specified percentage"""
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service not available", ephemeral=True)
            return

        if percentage not in [25, 50, 75, 100]:
            await interaction.response.send_message("❌ Percentage must be 25, 50, 75, or 100", ephemeral=True)
            return

        if confirm != "yes":
            await interaction.response.send_message(
                f"⚠️ This will close {percentage}% of {symbol} {side} position! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)
            symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Get position
            position = position_manager.get_position(user_id, symbol, side)
            if not position or position['status'] != 'open':
                await interaction.followup.send(f"❌ No open {side} position found for {symbol}")
                return

            # Calculate amount to close
            position_amount = abs(position['amount'])
            close_amount = position_amount * (percentage / 100)

            # Determine order side (opposite of position)
            order_side = 'sell' if side == 'LONG' else 'buy'

            # Place market order to close position
            result = self.trading_service.place_market_order(
                symbol=symbol,
                side=order_side,
                amount=close_amount,
                position_side=side
            )

            if result['success']:
                # Calculate estimated P&L for closed portion
                current_price = position.get('current_price', position.get('entry_price', 0))
                entry_price = position.get('entry_price', 0)

                if side == 'LONG':
                    estimated_pnl = (current_price - entry_price) * close_amount
                else:
                    estimated_pnl = (entry_price - current_price) * close_amount

                embed = discord.Embed(
                    title="✅ Position Partially Closed",
                    color=0x00ff88,
                    timestamp=datetime.now(timezone.utc)
                )

                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Side", value=side, inline=True)
                embed.add_field(name="Closed", value=f"{close_amount:.6f} ({percentage}%)", inline=True)
                embed.add_field(name="Remaining", value=f"{position_amount - close_amount:.6f} ({100-percentage}%)", inline=True)
                embed.add_field(name="Est. P&L", value=f"${estimated_pnl:,.2f}", inline=True)
                embed.add_field(name="Order ID", value=f"`{result['order_id']}`", inline=True)

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Failed to close position: {result['error']}")

        except Exception as e:
            logger.error(f"❌ Error closing position: {e}")
            await interaction.followup.send(f"❌ Error: {str(e)}")

    @app_commands.command(name="positions", description="View all open positions with P&L and metrics")
    @app_commands.describe(
        symbol="Filter by symbol (optional)",
        sort_by="Sort positions by: pnl, size, age, symbol"
    )
    async def view_positions(self, interaction: discord.Interaction,
                           symbol: Optional[str] = None,
                           sort_by: Optional[str] = "pnl"):
        """Display all open positions"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Get open positions
            positions = position_manager.get_user_positions(
                user_id, symbol=symbol, status='open'
            )

            if not positions:
                embed = discord.Embed(
                    title="📊 Open Positions",
                    description="No open positions found.",
                    color=0x95a5a6,
                    timestamp=datetime.now(timezone.utc)
                )
                await interaction.followup.send(embed=embed)
                return

            # Sort positions
            if sort_by == "pnl":
                positions.sort(key=lambda x: x.get('unrealized_pnl', 0), reverse=True)
            elif sort_by == "size":
                positions.sort(key=lambda x: abs(x.get('amount', 0)), reverse=True)
            elif sort_by == "age":
                positions.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            elif sort_by == "symbol":
                positions.sort(key=lambda x: x.get('symbol', ''))

            # Calculate totals
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in positions)
            total_margin_used = sum(p.get('margin_used', 0) for p in positions)

            # Create main embed
            embed = discord.Embed(
                title="📊 Open Positions",
                color=0x00ff88 if total_unrealized_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Add summary
            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="Portfolio Summary",
                value=f"**Total Unrealized P&L:** {pnl_color} ${total_unrealized_pnl:,.2f}\n"
                      f"**Total Margin Used:** ${total_margin_used:,.2f}\n"
                      f"**Open Positions:** {len(positions)}",
                inline=False
            )

            # Add individual positions (max 10 to avoid embed limits)
            for i, position in enumerate(positions[:10]):
                pnl = position.get('unrealized_pnl', 0)
                pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                # Calculate P&L percentage
                entry_value = position.get('amount', 0) * position.get('entry_price', 0)
                pnl_percent = (pnl / entry_value * 100) if entry_value > 0 else 0

                # Calculate position age
                created_at = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))
                age = datetime.now(timezone.utc) - created_at
                age_str = f"{age.days}d {age.seconds//3600}h" if age.days > 0 else f"{age.seconds//3600}h {(age.seconds%3600)//60}m"

                position_info = (
                    f"**Side:** {position.get('position_side', 'N/A')}\n"
                    f"**Size:** {position.get('amount', 0):.6f}\n"
                    f"**Entry:** ${position.get('entry_price', 0):,.2f}\n"
                    f"**Current:** ${position.get('current_price', 0):,.2f}\n"
                    f"**P&L:** {pnl_emoji} ${pnl:,.2f} ({pnl_percent:+.2f}%)\n"
                    f"**Age:** {age_str}"
                )

                embed.add_field(
                    name=f"{position.get('symbol', 'Unknown')} #{i+1}",
                    value=position_info,
                    inline=True
                )

            if len(positions) > 10:
                embed.add_field(
                    name="Note",
                    value=f"Showing 10 of {len(positions)} positions. Use symbol filter for specific positions.",
                    inline=False
                )

            embed.set_footer(text=f"Sorted by: {sort_by} | Use /pnl for detailed P&L analysis")
            await interaction.followup.send(embed=embed)

            logger.info(f"Positions displayed for {interaction.user}: {len(positions)} positions")

        except Exception as e:
            logger.error(f"Error in positions command: {e}")
            await interaction.followup.send(f"❌ Error retrieving positions: {str(e)}")

    @app_commands.command(name="pnl", description="Comprehensive P&L analysis and performance metrics")
    @app_commands.describe(
        period="Time period: today, week, month, all",
        symbol="Filter by symbol (optional)"
    )
    async def view_pnl(self, interaction: discord.Interaction,
                      period: Optional[str] = "all",
                      symbol: Optional[str] = None):
        """Display comprehensive P&L analysis"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Calculate time range
            now = datetime.now(timezone.utc)
            if period == "today":
                start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            elif period == "week":
                start_time = now - timedelta(days=7)
            elif period == "month":
                start_time = now - timedelta(days=30)
            else:  # all
                start_time = None

            # Get positions and orders for analysis
            all_positions = position_manager.get_user_positions(user_id, symbol=symbol)
            open_positions = [p for p in all_positions if p['status'] == 'open']
            closed_positions = [p for p in all_positions if p['status'] == 'closed']

            # Filter by time period if specified
            if start_time:
                closed_positions = [
                    p for p in closed_positions
                    if datetime.fromisoformat(p.get('updated_at', '').replace('Z', '+00:00')) >= start_time
                ]

            # Calculate metrics
            total_unrealized_pnl = sum(p.get('unrealized_pnl', 0) for p in open_positions)
            total_realized_pnl = sum(p.get('realized_pnl', 0) for p in closed_positions)
            total_pnl = total_unrealized_pnl + total_realized_pnl

            # Win/Loss analysis
            winning_positions = [p for p in closed_positions if p.get('realized_pnl', 0) > 0]
            losing_positions = [p for p in closed_positions if p.get('realized_pnl', 0) < 0]

            win_rate = (len(winning_positions) / len(closed_positions) * 100) if closed_positions else 0
            avg_win = sum(p.get('realized_pnl', 0) for p in winning_positions) / len(winning_positions) if winning_positions else 0
            avg_loss = sum(p.get('realized_pnl', 0) for p in losing_positions) / len(losing_positions) if losing_positions else 0

            # Create embed
            embed = discord.Embed(
                title=f"📈 P&L Analysis ({period.title()})",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Overall P&L
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(
                name="💰 Overall P&L",
                value=f"**Total P&L:** {pnl_color} ${total_pnl:,.2f}\n"
                      f"**Realized:** ${total_realized_pnl:,.2f}\n"
                      f"**Unrealized:** ${total_unrealized_pnl:,.2f}",
                inline=True
            )

            # Position Statistics
            embed.add_field(
                name="📊 Position Stats",
                value=f"**Open Positions:** {len(open_positions)}\n"
                      f"**Closed Positions:** {len(closed_positions)}\n"
                      f"**Total Positions:** {len(all_positions)}",
                inline=True
            )

            # Win/Loss Analysis
            if closed_positions:
                embed.add_field(
                    name="🎯 Win/Loss Analysis",
                    value=f"**Win Rate:** {win_rate:.1f}%\n"
                          f"**Avg Win:** ${avg_win:,.2f}\n"
                          f"**Avg Loss:** ${avg_loss:,.2f}\n"
                          f"**Profit Factor:** {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "**Profit Factor:** ∞",
                    inline=True
                )

            # Best/Worst Performers
            if closed_positions:
                best_position = max(closed_positions, key=lambda x: x.get('realized_pnl', 0))
                worst_position = min(closed_positions, key=lambda x: x.get('realized_pnl', 0))

                embed.add_field(
                    name="🏆 Best Trade",
                    value=f"**{best_position.get('symbol')}:** ${best_position.get('realized_pnl', 0):,.2f}",
                    inline=True
                )

                embed.add_field(
                    name="💥 Worst Trade",
                    value=f"**{worst_position.get('symbol')}:** ${worst_position.get('realized_pnl', 0):,.2f}",
                    inline=True
                )

            # Symbol breakdown if no specific symbol filter
            if not symbol and all_positions:
                symbol_pnl = {}
                for position in all_positions:
                    sym = position.get('symbol', 'Unknown')
                    pnl = position.get('realized_pnl', 0) + position.get('unrealized_pnl', 0)
                    symbol_pnl[sym] = symbol_pnl.get(sym, 0) + pnl

                # Top 3 symbols by P&L
                top_symbols = sorted(symbol_pnl.items(), key=lambda x: x[1], reverse=True)[:3]
                if top_symbols:
                    symbol_text = "\n".join([f"**{sym}:** ${pnl:,.2f}" for sym, pnl in top_symbols])
                    embed.add_field(
                        name="📈 Top Symbols",
                        value=symbol_text,
                        inline=True
                    )

            embed.set_footer(text="Use /history for detailed trading history")
            await interaction.followup.send(embed=embed)

            logger.info(f"P&L analysis displayed for {interaction.user}: {period} period")

        except Exception as e:
            logger.error(f"Error in pnl command: {e}")
            await interaction.followup.send(f"❌ Error calculating P&L: {str(e)}")

    @app_commands.command(name="close", description="Close specific position or all positions for a symbol")
    @app_commands.describe(
        symbol="Symbol to close all positions for",
        position_id="Specific position ID to close",
        confirm="Type 'yes' to confirm closing positions"
    )
    async def close_position(self, interaction: discord.Interaction,
                           symbol: Optional[str] = None,
                           position_id: Optional[str] = None,
                           confirm: Optional[str] = None):
        """Close positions"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not symbol and not position_id:
            await interaction.response.send_message(
                "❌ Please specify either a symbol or position_id to close",
                ephemeral=True
            )
            return

        if confirm != "yes":
            await interaction.response.send_message(
                "⚠️ This will close positions! Add `confirm:yes` to proceed",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Get positions to close
            if position_id:
                # Close specific position
                positions = position_manager.get_user_positions(user_id)
                target_positions = [p for p in positions if p.get('position_id') == position_id and p['status'] == 'open']
            else:
                # Close all positions for symbol
                target_positions = position_manager.get_user_positions(user_id, symbol=symbol, status='open')

            if not target_positions:
                await interaction.followup.send("❌ No matching open positions found")
                return

            # Create confirmation embed
            embed = discord.Embed(
                title="🔒 Position Close Request",
                description=f"Found {len(target_positions)} position(s) to close:",
                color=0xff6b35,
                timestamp=datetime.now(timezone.utc)
            )

            total_unrealized_pnl = 0
            for i, position in enumerate(target_positions[:5]):  # Show max 5
                pnl = position.get('unrealized_pnl', 0)
                total_unrealized_pnl += pnl
                pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                embed.add_field(
                    name=f"{position.get('symbol')} {position.get('position_side')}",
                    value=f"Size: {position.get('amount', 0):.6f}\n"
                          f"P&L: {pnl_emoji} ${pnl:,.2f}",
                    inline=True
                )

            if len(target_positions) > 5:
                embed.add_field(
                    name="...",
                    value=f"And {len(target_positions) - 5} more positions",
                    inline=True
                )

            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="Total Unrealized P&L",
                value=f"{pnl_color} ${total_unrealized_pnl:,.2f}",
                inline=False
            )

            embed.add_field(
                name="⚠️ Important",
                value="This is a simulation. In a real implementation, this would:\n"
                      "• Place market orders to close positions\n"
                      "• Update position status to 'closed'\n"
                      "• Calculate final realized P&L\n"
                      "• Send confirmation notifications",
                inline=False
            )

            await interaction.followup.send(embed=embed)

            # Log the close request
            logger.info(f"Position close requested by {interaction.user}: {len(target_positions)} positions")

        except Exception as e:
            logger.error(f"Error in close command: {e}")
            await interaction.followup.send(f"❌ Error closing positions: {str(e)}")

    @app_commands.command(name="history", description="Display trading history with performance metrics")
    @app_commands.describe(
        days="Number of days to look back (default: 30)",
        symbol="Filter by symbol (optional)",
        limit="Maximum number of trades to show (default: 20)"
    )
    async def trading_history(self, interaction: discord.Interaction,
                            days: Optional[int] = 30,
                            symbol: Optional[str] = None,
                            limit: Optional[int] = 20):
        """Display trading history"""

        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Calculate time range
            start_time = datetime.now(timezone.utc) - timedelta(days=days)

            # Get closed positions (completed trades)
            all_positions = position_manager.get_user_positions(user_id, symbol=symbol)
            closed_positions = [
                p for p in all_positions
                if p['status'] == 'closed' and
                datetime.fromisoformat(p.get('updated_at', '').replace('Z', '+00:00')) >= start_time
            ]

            # Sort by close time (most recent first)
            closed_positions.sort(
                key=lambda x: datetime.fromisoformat(x.get('updated_at', '').replace('Z', '+00:00')),
                reverse=True
            )

            if not closed_positions:
                embed = discord.Embed(
                    title="📜 Trading History",
                    description=f"No completed trades found in the last {days} days.",
                    color=0x95a5a6,
                    timestamp=datetime.now(timezone.utc)
                )
                await interaction.followup.send(embed=embed)
                return

            # Calculate summary metrics
            total_trades = len(closed_positions)
            total_pnl = sum(p.get('realized_pnl', 0) for p in closed_positions)
            winning_trades = [p for p in closed_positions if p.get('realized_pnl', 0) > 0]
            losing_trades = [p for p in closed_positions if p.get('realized_pnl', 0) < 0]

            win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
            avg_win = sum(p.get('realized_pnl', 0) for p in winning_trades) / len(winning_trades) if winning_trades else 0
            avg_loss = sum(p.get('realized_pnl', 0) for p in losing_trades) / len(losing_trades) if losing_trades else 0

            # Create main embed
            embed = discord.Embed(
                title=f"📜 Trading History ({days} days)",
                color=0x00ff88 if total_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Summary statistics
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            embed.add_field(
                name="📊 Summary",
                value=f"**Total Trades:** {total_trades}\n"
                      f"**Total P&L:** {pnl_color} ${total_pnl:,.2f}\n"
                      f"**Win Rate:** {win_rate:.1f}%\n"
                      f"**Avg Win:** ${avg_win:,.2f}\n"
                      f"**Avg Loss:** ${avg_loss:,.2f}",
                inline=True
            )

            # Recent trades (limited to avoid embed size limits)
            recent_trades = closed_positions[:min(limit, 10)]

            for i, position in enumerate(recent_trades):
                pnl = position.get('realized_pnl', 0)
                pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                # Calculate holding time
                created_at = datetime.fromisoformat(position.get('created_at', '').replace('Z', '+00:00'))
                updated_at = datetime.fromisoformat(position.get('updated_at', '').replace('Z', '+00:00'))
                holding_time = updated_at - created_at

                if holding_time.days > 0:
                    time_str = f"{holding_time.days}d {holding_time.seconds//3600}h"
                else:
                    time_str = f"{holding_time.seconds//3600}h {(holding_time.seconds%3600)//60}m"

                trade_info = (
                    f"**{position.get('position_side')}** {position.get('amount', 0):.6f}\n"
                    f"**P&L:** {pnl_emoji} ${pnl:,.2f}\n"
                    f"**Time:** {time_str}\n"
                    f"**Closed:** {updated_at.strftime('%m/%d %H:%M')}"
                )

                embed.add_field(
                    name=f"{position.get('symbol')} #{i+1}",
                    value=trade_info,
                    inline=True
                )

            if len(closed_positions) > 10:
                embed.add_field(
                    name="📋 Note",
                    value=f"Showing {len(recent_trades)} of {len(closed_positions)} trades. "
                          f"Use filters to narrow results.",
                    inline=False
                )

            embed.set_footer(text=f"Use /pnl for detailed P&L analysis")
            await interaction.followup.send(embed=embed)

            logger.info(f"Trading history displayed for {interaction.user}: {len(closed_positions)} trades")

        except Exception as e:
            logger.error(f"Error in history command: {e}")
            await interaction.followup.send(f"❌ Error retrieving history: {str(e)}")

async def setup(bot):
    await bot.add_cog(AdvancedTradingCommands(bot))
