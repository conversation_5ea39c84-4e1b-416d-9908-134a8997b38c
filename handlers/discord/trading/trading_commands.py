import logging
import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional

from services.trading.trading_service import BinanceFuturesTrading
from services.trading.order_tracker import order_tracker
from services.trading.notification_service import notification_service
from services.trading.realtime_monitor import realtime_monitor
from services.data.database import trading_db
from services.core.symbol_service import smart_normalize_symbol

logger = logging.getLogger(__name__)

class TradingCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.trading_service = None
        self.trade_channel_name = "trade"  # Tên channel được phép trading

    async def cog_load(self):
        """Initialize trading service when cog loads"""
        try:
            self.trading_service = BinanceFuturesTrading()

            # Set trading service for unified order tracker
            order_tracker.trading_service = self.trading_service

            # Set bot for notification service
            notification_service.set_bot(self.bot)

            # Register order handler with realtime monitor
            realtime_monitor.register_order_handler(order_tracker.handle_websocket_order_update)

            # Start realtime monitoring
            await realtime_monitor.start()

            # Start order reconciliation
            await order_tracker.start_reconciliation()

            logger.info("✅ Trading service and unified tracking system initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize trading service: {e}")
            self.trading_service = None

    def _check_trade_channel(self, interaction: discord.Interaction) -> bool:
        """Kiểm tra xem lệnh có được gọi trong channel 'trade' không"""
        if not interaction.channel:
            return False
        return interaction.channel.name == self.trade_channel_name



    async def _get_current_price(self, symbol: str) -> float:
        """Lấy giá hiện tại của symbol"""
        try:
            ticker = self.trading_service.exchange.fetch_ticker(symbol)
            return float(ticker['last'])
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            raise

    def _calculate_amount_from_value(self, price: float, value_usdt: float) -> float:
        """Tính số lượng từ giá trị USDT"""
        return value_usdt / price

    async def _create_order_embed(self, result: dict, order_type: str, symbol: str,
                                side: str, amount: float, price: float = None) -> discord.Embed:
        """Tạo embed cho kết quả đặt lệnh"""
        if result['success']:
            embed = discord.Embed(
                title=f"✅ {order_type.upper()} Order Placed",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value=side.upper(), inline=True)
            embed.add_field(name="Amount", value=f"{amount:.6f}", inline=True)

            if price:
                embed.add_field(name="Price", value=f"${price:,.2f}", inline=True)
                embed.add_field(name="Value", value=f"${amount * price:,.2f}", inline=True)

            embed.add_field(name="Order ID", value=result['order_id'], inline=False)
            embed.add_field(name="Status", value=result['status'], inline=True)

        else:
            embed = discord.Embed(
                title=f"❌ {order_type.upper()} Order Failed",
                description=f"Error: {result['error']}",
                color=0xff4444,
                timestamp=discord.utils.utcnow()
            )

        embed.set_footer(text="Binance Futures Trading")
        return embed

    @app_commands.command(name="l", description="Long limit - /l btc 100k 100 [amount] [tp] [sl]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        price="Giá limit (vd: 100k, 100000)",
        value="Giá trị USDT (vd: 100)",
        amount="Số lượng (tùy chọn, nếu có thì dùng amount thay vì value)",
        tp="Take profit price (tùy chọn)",
        sl="Stop loss price (tùy chọn)"
    )
    async def long_limit(self, interaction: discord.Interaction, symbol: str, price: str,
                        value: float, amount: Optional[float] = None,
                        tp: Optional[float] = None, sl: Optional[float] = None):
        """Đặt lệnh LONG limit với TP/SL tùy chọn"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Parse price (hỗ trợ 100k = 100000)
            entry_price = self._parse_price(price)

            # Tính amount: nếu có amount thì dùng amount, không thì dùng value
            if amount is not None and amount > 0:
                trade_amount = amount
                trade_value = amount * entry_price
            else:
                if value <= 0:
                    await interaction.followup.send("❌ Cần nhập value > 0")
                    return
                trade_amount = self._calculate_amount_from_value(entry_price, value)
                trade_value = value

            # Đặt lệnh chính
            main_result = self.trading_service.place_limit_order(
                symbol=symbol,
                side='buy',
                amount=trade_amount,
                price=entry_price,
                position_side='LONG'
            )

            if not main_result['success']:
                embed = discord.Embed(
                    title="❌ LONG Order Failed",
                    description=f"Error: {main_result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=embed)
                return

            # Ensure user exists in database
            trading_db.create_user(str(interaction.user.id), str(interaction.user))

            # Start tracking the main order
            metadata = {
                'command': f'/l {symbol} {price} {value}',
                'channel_id': str(interaction.channel.id),
                'strategy': 'manual',
                'symbol': symbol,
                'amount': trade_amount,
                'position_side': 'LONG'
            }

            order_tracker.track_order(main_result, str(interaction.user.id), metadata)

            # Tạo embed kết quả
            embed = discord.Embed(
                title="✅ LONG Limit Order Placed",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value="BUY", inline=True)
            embed.add_field(name="Amount", value=f"{trade_amount:.6f}", inline=True)
            embed.add_field(name="Entry Price", value=f"${entry_price:,.2f}", inline=True)
            embed.add_field(name="Value", value=f"${trade_value:,.2f}", inline=True)
            embed.add_field(name="Position", value="LONG", inline=True)
            embed.add_field(name="Main Order ID", value=f"`{main_result['order_id']}`", inline=False)

            # Đặt TP/SL nếu có
            tp_sl_info = ""
            if tp and tp > 0:
                tp_result = self.trading_service.place_take_profit_order(
                    symbol=symbol, side='sell', amount=trade_amount,
                    stop_price=tp, position_side='LONG', reduce_only=True
                )
                if tp_result['success']:
                    tp_sl_info += f"📈 **TP:** ${tp:,.2f} - `{tp_result['order_id']}`\n"
                    embed.add_field(name="📈 Take Profit", value=f"${tp:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **TP Error:** {tp_result['error']}\n"

            if sl and sl > 0:
                sl_result = self.trading_service.place_stop_loss_order(
                    symbol=symbol, side='sell', amount=trade_amount,
                    stop_price=sl, position_side='LONG', reduce_only=True
                )
                if sl_result['success']:
                    tp_sl_info += f"📉 **SL:** ${sl:,.2f} - `{sl_result['order_id']}`"
                    embed.add_field(name="📉 Stop Loss", value=f"${sl:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **SL Error:** {sl_result['error']}"

            if tp_sl_info:
                embed.add_field(name="TP/SL Orders", value=tp_sl_info, inline=False)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            # We're not sending a separate notification through notification_service
            # since we already sent the embed directly to the channel
            # This prevents duplicate notifications

            logger.info(f"Long limit by {interaction.user}: {symbol} {trade_amount} @ {entry_price}")

        except Exception as e:
            logger.error(f"Error in long_limit command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    def _parse_price(self, price_str: str) -> float:
        """Parse price string (hỗ trợ 100k = 100000)"""
        price_str = price_str.lower().strip()

        if price_str.endswith('k'):
            return float(price_str[:-1]) * 1000
        elif price_str.endswith('m'):
            return float(price_str[:-1]) * 1000000
        else:
            return float(price_str)

    @app_commands.command(name="s", description="Short limit - /s btc 100k 100 [amount] [tp] [sl]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        price="Giá limit (vd: 100k, 100000)",
        value="Giá trị USDT (vd: 100)",
        amount="Số lượng (tùy chọn, nếu có thì dùng amount thay vì value)",
        tp="Take profit price (tùy chọn)",
        sl="Stop loss price (tùy chọn)"
    )
    async def short_limit(self, interaction: discord.Interaction, symbol: str, price: str,
                         value: float, amount: Optional[float] = None,
                         tp: Optional[float] = None, sl: Optional[float] = None):
        """Đặt lệnh SHORT limit với TP/SL tùy chọn"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Parse price (hỗ trợ 100k = 100000)
            entry_price = self._parse_price(price)

            # Tính amount: nếu có amount thì dùng amount, không thì dùng value
            if amount is not None and amount > 0:
                trade_amount = amount
                trade_value = amount * entry_price
            else:
                if value <= 0:
                    await interaction.followup.send("❌ Cần nhập value > 0")
                    return
                trade_amount = self._calculate_amount_from_value(entry_price, value)
                trade_value = value

            # Đặt lệnh chính
            main_result = self.trading_service.place_limit_order(
                symbol=symbol,
                side='sell',
                amount=trade_amount,
                price=entry_price,
                position_side='SHORT'
            )

            if not main_result['success']:
                embed = discord.Embed(
                    title="❌ SHORT Order Failed",
                    description=f"Error: {main_result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=embed)
                return

            # Tạo embed kết quả
            embed = discord.Embed(
                title="✅ SHORT Limit Order Placed",
                color=0xff4444,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value="SELL", inline=True)
            embed.add_field(name="Amount", value=f"{trade_amount:.6f}", inline=True)
            embed.add_field(name="Entry Price", value=f"${entry_price:,.2f}", inline=True)
            embed.add_field(name="Value", value=f"${trade_value:,.2f}", inline=True)
            embed.add_field(name="Position", value="SHORT", inline=True)
            embed.add_field(name="Main Order ID", value=f"`{main_result['order_id']}`", inline=False)

            # Đặt TP/SL nếu có
            tp_sl_info = ""
            if tp and tp > 0:
                tp_result = self.trading_service.place_take_profit_order(
                    symbol=symbol, side='buy', amount=trade_amount,
                    stop_price=tp, position_side='SHORT', reduce_only=True
                )
                if tp_result['success']:
                    tp_sl_info += f"📈 **TP:** ${tp:,.2f} - `{tp_result['order_id']}`\n"
                    embed.add_field(name="📈 Take Profit", value=f"${tp:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **TP Error:** {tp_result['error']}\n"

            if sl and sl > 0:
                sl_result = self.trading_service.place_stop_loss_order(
                    symbol=symbol, side='buy', amount=trade_amount,
                    stop_price=sl, position_side='SHORT', reduce_only=True
                )
                if sl_result['success']:
                    tp_sl_info += f"📉 **SL:** ${sl:,.2f} - `{sl_result['order_id']}`"
                    embed.add_field(name="📉 Stop Loss", value=f"${sl:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **SL Error:** {sl_result['error']}"

            if tp_sl_info:
                embed.add_field(name="TP/SL Orders", value=tp_sl_info, inline=False)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Short limit by {interaction.user}: {symbol} {trade_amount} @ {entry_price}")

        except Exception as e:
            logger.error(f"Error in short_limit command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="ql", description="Quick long - /ql btc 100 [amount] [tp] [sl]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        value="Giá trị USDT (vd: 100)",
        amount="Số lượng (tùy chọn, nếu có thì dùng amount thay vì value)",
        tp="Take profit price (tùy chọn)",
        sl="Stop loss price (tùy chọn)"
    )
    async def quick_long(self, interaction: discord.Interaction, symbol: str, value: float,
                        amount: Optional[float] = None, tp: Optional[float] = None,
                        sl: Optional[float] = None):
        """Đặt lệnh LONG quick limit - giá hiện tại trừ 0.2%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Lấy giá hiện tại và tính giá limit (giảm 0.2%)
            current_price = await self._get_current_price(symbol)
            entry_price = current_price * 0.998  # Giảm 0.2%

            # Tính amount: nếu có amount thì dùng amount, không thì dùng value
            if amount is not None and amount > 0:
                trade_amount = amount
                trade_value = amount * entry_price
            else:
                if value <= 0:
                    await interaction.followup.send("❌ Cần nhập value > 0")
                    return
                trade_amount = self._calculate_amount_from_value(entry_price, value)
                trade_value = value

            # Đặt lệnh chính
            main_result = self.trading_service.place_limit_order(
                symbol=symbol,
                side='buy',
                amount=trade_amount,
                price=entry_price,
                position_side='LONG'
            )

            if not main_result['success']:
                embed = discord.Embed(
                    title="❌ QUICK LONG Order Failed",
                    description=f"Error: {main_result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=embed)
                return

            # Tạo embed kết quả
            embed = discord.Embed(
                title="✅ QUICK LONG Order Placed",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value="BUY", inline=True)
            embed.add_field(name="Amount", value=f"{trade_amount:.6f}", inline=True)
            embed.add_field(name="Current Price", value=f"${current_price:,.2f}", inline=True)
            embed.add_field(name="Entry Price", value=f"${entry_price:,.2f} (-0.2%)", inline=True)
            embed.add_field(name="Value", value=f"${trade_value:,.2f}", inline=True)
            embed.add_field(name="Main Order ID", value=f"`{main_result['order_id']}`", inline=False)

            # Đặt TP/SL nếu có
            tp_sl_info = ""
            if tp and tp > 0:
                tp_result = self.trading_service.place_take_profit_order(
                    symbol=symbol, side='sell', amount=trade_amount,
                    stop_price=tp, position_side='LONG', reduce_only=True
                )
                if tp_result['success']:
                    tp_sl_info += f"📈 **TP:** ${tp:,.2f} - `{tp_result['order_id']}`\n"
                    embed.add_field(name="📈 Take Profit", value=f"${tp:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **TP Error:** {tp_result['error']}\n"

            if sl and sl > 0:
                sl_result = self.trading_service.place_stop_loss_order(
                    symbol=symbol, side='sell', amount=trade_amount,
                    stop_price=sl, position_side='LONG', reduce_only=True
                )
                if sl_result['success']:
                    tp_sl_info += f"📉 **SL:** ${sl:,.2f} - `{sl_result['order_id']}`"
                    embed.add_field(name="📉 Stop Loss", value=f"${sl:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **SL Error:** {sl_result['error']}"

            if tp_sl_info:
                embed.add_field(name="TP/SL Orders", value=tp_sl_info, inline=False)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Quick long by {interaction.user}: {symbol} {trade_amount} @ {entry_price} (current: {current_price})")

        except Exception as e:
            logger.error(f"Error in quick_long command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="qs", description="Quick short - /qs btc 100 [amount] [tp] [sl]")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth)",
        value="Giá trị USDT (vd: 100)",
        amount="Số lượng (tùy chọn, nếu có thì dùng amount thay vì value)",
        tp="Take profit price (tùy chọn)",
        sl="Stop loss price (tùy chọn)"
    )
    async def quick_short(self, interaction: discord.Interaction, symbol: str, value: float,
                         amount: Optional[float] = None, tp: Optional[float] = None,
                         sl: Optional[float] = None):
        """Đặt lệnh SHORT quick limit - giá hiện tại cộng 0.2%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Lấy giá hiện tại và tính giá limit (tăng 0.2%)
            current_price = await self._get_current_price(symbol)
            entry_price = current_price * 1.002  # Tăng 0.2%

            # Tính amount: nếu có amount thì dùng amount, không thì dùng value
            if amount is not None and amount > 0:
                trade_amount = amount
                trade_value = amount * entry_price
            else:
                if value <= 0:
                    await interaction.followup.send("❌ Cần nhập value > 0")
                    return
                trade_amount = self._calculate_amount_from_value(entry_price, value)
                trade_value = value

            # Đặt lệnh chính
            main_result = self.trading_service.place_limit_order(
                symbol=symbol,
                side='sell',
                amount=trade_amount,
                price=entry_price,
                position_side='SHORT'
            )

            if not main_result['success']:
                embed = discord.Embed(
                    title="❌ QUICK SHORT Order Failed",
                    description=f"Error: {main_result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=embed)
                return

            # Tạo embed kết quả
            embed = discord.Embed(
                title="✅ QUICK SHORT Order Placed",
                color=0xff4444,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value="SELL", inline=True)
            embed.add_field(name="Amount", value=f"{trade_amount:.6f}", inline=True)
            embed.add_field(name="Current Price", value=f"${current_price:,.2f}", inline=True)
            embed.add_field(name="Entry Price", value=f"${entry_price:,.2f} (+0.2%)", inline=True)
            embed.add_field(name="Value", value=f"${trade_value:,.2f}", inline=True)
            embed.add_field(name="Main Order ID", value=f"`{main_result['order_id']}`", inline=False)

            # Đặt TP/SL nếu có
            tp_sl_info = ""
            if tp and tp > 0:
                tp_result = self.trading_service.place_take_profit_order(
                    symbol=symbol, side='buy', amount=trade_amount,
                    stop_price=tp, position_side='SHORT', reduce_only=True
                )
                if tp_result['success']:
                    tp_sl_info += f"📈 **TP:** ${tp:,.2f} - `{tp_result['order_id']}`\n"
                    embed.add_field(name="📈 Take Profit", value=f"${tp:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **TP Error:** {tp_result['error']}\n"

            if sl and sl > 0:
                sl_result = self.trading_service.place_stop_loss_order(
                    symbol=symbol, side='buy', amount=trade_amount,
                    stop_price=sl, position_side='SHORT', reduce_only=True
                )
                if sl_result['success']:
                    tp_sl_info += f"📉 **SL:** ${sl:,.2f} - `{sl_result['order_id']}`"
                    embed.add_field(name="📉 Stop Loss", value=f"${sl:,.2f}", inline=True)
                else:
                    tp_sl_info += f"❌ **SL Error:** {sl_result['error']}"

            if tp_sl_info:
                embed.add_field(name="TP/SL Orders", value=tp_sl_info, inline=False)

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Quick short by {interaction.user}: {symbol} {trade_amount} @ {entry_price} (current: {current_price})")

        except Exception as e:
            logger.error(f"Error in quick_short command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="cancel", description="Hủy lệnh - /cancel symbol order_id")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth, btcusdt)",
        order_id="Order ID cần hủy"
    )
    async def cancel_order(self, interaction: discord.Interaction, symbol: str, order_id: str):
        """Hủy lệnh"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Hủy lệnh
            result = self.trading_service.cancel_order(symbol, order_id)

            if result['success']:
                embed = discord.Embed(
                    title="✅ Order Cancelled",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )
                embed.add_field(name="Symbol", value=symbol, inline=True)
                embed.add_field(name="Order ID", value=order_id, inline=True)
            else:
                embed = discord.Embed(
                    title="❌ Cancel Failed",
                    description=f"Error: {result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Cancel order by {interaction.user}: {symbol} {order_id}")

        except Exception as e:
            logger.error(f"Error in cancel_order command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="orders", description="Xem lệnh đang chờ - /orders [symbol]")
    @app_commands.describe(symbol="Symbol để lọc (tùy chọn)")
    async def check_orders(self, interaction: discord.Interaction, symbol: Optional[str] = None):
        """Xem lệnh đang chờ"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Chuẩn hóa symbol nếu có
            if symbol:
                symbol = smart_normalize_symbol(symbol)

            # Lấy open orders
            result = self.trading_service.get_open_orders(symbol)

            if result['success']:
                orders = result['orders']

                if not orders:
                    embed = discord.Embed(
                        title="📋 Open Orders",
                        description="Không có lệnh đang chờ",
                        color=0x3498db,
                        timestamp=discord.utils.utcnow()
                    )
                else:
                    embed = discord.Embed(
                        title=f"📋 Open Orders ({len(orders)})",
                        color=0x3498db,
                        timestamp=discord.utils.utcnow()
                    )

                    for i, order in enumerate(orders[:10]):  # Giới hạn 10 orders
                        side_emoji = "🟢" if order['side'] == 'buy' else "🔴"
                        order_info = (
                            f"{side_emoji} **{order['side'].upper()}** {order['symbol']}\n"
                            f"Amount: {order['amount']:.6f}\n"
                            f"Price: ${order.get('price', 'Market'):,.2f}\n"
                            f"ID: `{order['id']}`"
                        )

                        embed.add_field(
                            name=f"Order {i+1}",
                            value=order_info,
                            inline=True
                        )

                        if (i + 1) % 3 == 0:  # Xuống dòng sau mỗi 3 orders
                            embed.add_field(name="\u200b", value="\u200b", inline=False)
            else:
                embed = discord.Embed(
                    title="❌ Error",
                    description=f"Lỗi lấy orders: {result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Check orders by {interaction.user}: {symbol or 'all'}")

        except Exception as e:
            logger.error(f"Error in check_orders command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="balance", description="Xem balance tài khoản")
    async def check_balance(self, interaction: discord.Interaction):
        """Xem balance tài khoản"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        await interaction.response.defer()

        try:
            # Lấy balance
            result = self.trading_service.get_account_balance()

            if result['success']:
                embed = discord.Embed(
                    title="💰 Account Balance",
                    color=0x00ff88,
                    timestamp=discord.utils.utcnow()
                )

                embed.add_field(name="Free USDT", value=f"${result['free_usdt']:,.2f}", inline=True)
                embed.add_field(name="Total USDT", value=f"${result['total_usdt']:,.2f}", inline=True)

            else:
                embed = discord.Embed(
                    title="❌ Error",
                    description=f"Lỗi lấy balance: {result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )

            embed.set_footer(text="Binance Futures Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"Check balance by {interaction.user}")

        except Exception as e:
            logger.error(f"Error in check_balance command: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    async def _place_trade_with_tp_sl(self, interaction: discord.Interaction, symbol: str,
                                    price: float, value_usdt: float, side: str,
                                    tp_percent: float, sl_percent: float, trade_type: str):
        """Đặt lệnh với TP/SL tự động"""
        try:
            # Chuẩn hóa symbol
            symbol = smart_normalize_symbol(symbol)

            # Tính amount từ value
            amount = self._calculate_amount_from_value(price, value_usdt)

            # Xác định position side
            position_side = 'LONG' if side == 'buy' else 'SHORT'

            # Đặt lệnh chính
            main_result = self.trading_service.place_limit_order(
                symbol=symbol,
                side=side,
                amount=amount,
                price=price,
                position_side=position_side
            )

            if not main_result['success']:
                embed = discord.Embed(
                    title=f"❌ {trade_type.upper()} Order Failed",
                    description=f"Error: {main_result['error']}",
                    color=0xff4444,
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=embed)
                return

            # Tính giá TP và SL
            if side == 'buy':  # Long position
                tp_price = price * (1 + tp_percent / 100)
                sl_price = price * (1 - sl_percent / 100)
                tp_side = 'sell'
                sl_side = 'sell'
            else:  # Short position
                tp_price = price * (1 - tp_percent / 100)
                sl_price = price * (1 + sl_percent / 100)
                tp_side = 'buy'
                sl_side = 'buy'

            # Đặt lệnh TP
            tp_result = self.trading_service.place_take_profit_order(
                symbol=symbol,
                side=tp_side,
                amount=amount,
                stop_price=tp_price,
                position_side=position_side,
                reduce_only=True
            )

            # Đặt lệnh SL
            sl_result = self.trading_service.place_stop_loss_order(
                symbol=symbol,
                side=sl_side,
                amount=amount,
                stop_price=sl_price,
                position_side=position_side,
                reduce_only=True
            )

            # Tạo embed kết quả
            embed = discord.Embed(
                title=f"✅ {trade_type.upper()} {side.upper()} Setup Complete",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            embed.add_field(name="Symbol", value=symbol, inline=True)
            embed.add_field(name="Side", value=side.upper(), inline=True)
            embed.add_field(name="Amount", value=f"{amount:.6f}", inline=True)

            embed.add_field(name="Entry Price", value=f"${price:,.2f}", inline=True)
            embed.add_field(name="Value", value=f"${value_usdt:,.2f}", inline=True)
            embed.add_field(name="Position", value=position_side, inline=True)

            embed.add_field(name="📈 Take Profit", value=f"${tp_price:,.2f} (+{tp_percent}%)", inline=True)
            embed.add_field(name="📉 Stop Loss", value=f"${sl_price:,.2f} (-{sl_percent}%)", inline=True)
            embed.add_field(name="R:R Ratio", value=f"1:{tp_percent/sl_percent:.1f}", inline=True)

            # Thông tin orders
            order_info = f"**Main Order:** `{main_result['order_id']}`\n"
            if tp_result['success']:
                order_info += f"**TP Order:** `{tp_result['order_id']}`\n"
            else:
                order_info += f"**TP Error:** {tp_result['error']}\n"

            if sl_result['success']:
                order_info += f"**SL Order:** `{sl_result['order_id']}`"
            else:
                order_info += f"**SL Error:** {sl_result['error']}"

            embed.add_field(name="Order IDs", value=order_info, inline=False)

            embed.set_footer(text=f"Binance Futures {trade_type.title()} Trading")
            await interaction.followup.send(embed=embed)

            logger.info(f"{trade_type} {side} by {interaction.user}: {symbol} {amount} @ {price} (TP: {tp_price}, SL: {sl_price})")

        except Exception as e:
            logger.error(f"Error in {trade_type} trade: {e}")
            await interaction.followup.send(f"❌ Lỗi: {str(e)}")

    @app_commands.command(name="scl", description="Scalping LONG - /scl symbol price value (TP: 1.5%, SL: 1%)")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth, btcusdt)",
        price="Giá entry",
        value="Giá trị USDT"
    )
    async def scalping_long(self, interaction: discord.Interaction, symbol: str, price: float, value: float):
        """Đặt lệnh scalping long với TP 1.5% và SL 1%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        if value <= 0:
            await interaction.response.send_message("❌ Giá trị phải > 0", ephemeral=True)
            return

        await interaction.response.defer()

        await self._place_trade_with_tp_sl(
            interaction, symbol, price, value, 'buy',
            tp_percent=1.5, sl_percent=1.0, trade_type='scalping'
        )

    @app_commands.command(name="scs", description="Scalping SHORT - /scs symbol price value (TP: 1.5%, SL: 1%)")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth, btcusdt)",
        price="Giá entry",
        value="Giá trị USDT"
    )
    async def scalping_short(self, interaction: discord.Interaction, symbol: str, price: float, value: float):
        """Đặt lệnh scalping short với TP 1.5% và SL 1%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        if value <= 0:
            await interaction.response.send_message("❌ Giá trị phải > 0", ephemeral=True)
            return

        await interaction.response.defer()

        await self._place_trade_with_tp_sl(
            interaction, symbol, price, value, 'sell',
            tp_percent=1.5, sl_percent=1.0, trade_type='scalping'
        )

    @app_commands.command(name="swl", description="Swing LONG - /swl symbol price value (TP: 8%, SL: 5%)")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth, btcusdt)",
        price="Giá entry",
        value="Giá trị USDT"
    )
    async def swing_long(self, interaction: discord.Interaction, symbol: str, price: float, value: float):
        """Đặt lệnh swing long với TP 8% và SL 5%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        if value <= 0:
            await interaction.response.send_message("❌ Giá trị phải > 0", ephemeral=True)
            return

        await interaction.response.defer()

        await self._place_trade_with_tp_sl(
            interaction, symbol, price, value, 'buy',
            tp_percent=8.0, sl_percent=5.0, trade_type='swing'
        )

    @app_commands.command(name="sws", description="Swing SHORT - /sws symbol price value (TP: 8%, SL: 5%)")
    @app_commands.describe(
        symbol="Symbol (vd: btc, eth, btcusdt)",
        price="Giá entry",
        value="Giá trị USDT"
    )
    async def swing_short(self, interaction: discord.Interaction, symbol: str, price: float, value: float):
        """Đặt lệnh swing short với TP 8% và SL 5%"""

        # Kiểm tra channel
        if not self._check_trade_channel(interaction):
            await interaction.response.send_message(
                f"❌ Lệnh trading chỉ được phép trong channel #{self.trade_channel_name}",
                ephemeral=True
            )
            return

        if not self.trading_service:
            await interaction.response.send_message("❌ Trading service chưa sẵn sàng", ephemeral=True)
            return

        if value <= 0:
            await interaction.response.send_message("❌ Giá trị phải > 0", ephemeral=True)
            return

        await interaction.response.defer()

        await self._place_trade_with_tp_sl(
            interaction, symbol, price, value, 'sell',
            tp_percent=8.0, sl_percent=5.0, trade_type='swing'
        )

async def setup(bot):
    await bot.add_cog(TradingCommands(bot))
