#!/usr/bin/env python3
"""
Enhanced Position Control Commands for Binance Futures Trading Bot

Commands implemented:
1. /tp [symbol] [side] [price] [percentage] - Set take profit order
2. /sl [symbol] [side] [price] [percentage] - Set stop loss order
3. /closeall [confirm:yes] - Close all open positions
4. /closeside [side] [confirm:yes] - Close all positions on one side
5. /closepos [symbol] [side] [percentage] [confirm:yes] - Close specific position

Integration:
- Uses order_tracker for order tracking
- Uses position_manager for position data
- Uses trading_service for order execution
- Uses notification_service for Discord responses
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
from typing import Optional

from services.trading.trading_service import BinanceFuturesTrading
from services.trading.order_tracker import order_tracker
from services.trading.position_manager import position_manager
from services.trading.notification_service import notification_service
from services.core.symbol_service import smart_normalize_symbol
from utils.config import load_config

logger = logging.getLogger(__name__)

class PositionControlCommands(commands.Cog):
    """Enhanced Position Control Commands Cog"""

    def __init__(self, bot):
        self.bot = bot
        self.trading_service = None
        self.config = load_config()

    async def cog_load(self):
        """Initialize trading service when cog loads"""
        try:
            self.trading_service = BinanceFuturesTrading()

            # Set dependencies
            order_tracker.trading_service = self.trading_service
            position_manager.trading_service = self.trading_service
            notification_service.set_bot(self.bot)

            logger.info("✅ Position Control Commands initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Position Control Commands: {e}")
            self.trading_service = None

    def _check_trading_channel(self, interaction: discord.Interaction) -> bool:
        """Check if command is used in trading channel"""
        allowed_channels = self.config.get('discord', {}).get('trading_channels', ['trade'])
        return interaction.channel.name in allowed_channels

    async def _create_position_embed(self, title: str, description: str, color: int = 0x00ff88) -> discord.Embed:
        """Create standardized position control embed"""
        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=discord.utils.utcnow()
        )
        embed.set_footer(text="Binance Futures • Position Control")
        return embed

    # ==================== TAKE PROFIT COMMAND ====================

    @app_commands.command(name="tp", description="Set take profit order for position")
    @app_commands.describe(
        symbol="Trading symbol (e.g., btc, eth, btcusdt)",
        side="Position side (LONG/SHORT)",
        price="Take profit price level",
        percentage="Percentage of position to close (default: 100%)"
    )
    async def take_profit(
        self,
        interaction: discord.Interaction,
        symbol: str,
        side: str,
        price: float,
        percentage: Optional[float] = 100.0
    ):
        """Set take profit order for position"""
        try:
            # Check channel
            if not self._check_trading_channel(interaction):
                await interaction.response.send_message("❌ This command can only be used in trading channels.", ephemeral=True)
                return

            # Validate inputs
            if side.upper() not in ['LONG', 'SHORT']:
                await interaction.response.send_message("❌ Side must be LONG or SHORT", ephemeral=True)
                return

            if not (0 < percentage <= 100):
                await interaction.response.send_message("❌ Percentage must be between 1-100", ephemeral=True)
                return

            await interaction.response.defer()

            # Normalize symbol
            normalized_symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Check if position exists
            positions = position_manager.get_user_positions(str(interaction.user.id), status='open')
            target_position = None

            for pos in positions:
                if pos['symbol'] == normalized_symbol and pos['side'] == side:
                    target_position = pos
                    break

            if not target_position:
                embed = await self._create_position_embed(
                    "❌ Take Profit Failed",
                    f"No {side} position found for {normalized_symbol}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)
                return

            # Calculate TP amount
            position_size = abs(float(target_position['amount']))
            tp_amount = position_size * (percentage / 100.0)

            # Place take profit order
            result = self.trading_service.place_take_profit_order(
                symbol=normalized_symbol,
                side='sell' if side == 'LONG' else 'buy',
                amount=tp_amount,
                price=price,
                position_side=side
            )

            if result['success']:
                # Track the TP order
                metadata = {
                    'symbol': normalized_symbol,
                    'side': 'sell' if side == 'LONG' else 'buy',
                    'amount': tp_amount,
                    'price': price,
                    'order_type': 'take_profit',
                    'position_side': side,
                    'percentage': percentage,
                    'command': 'tp'
                }

                order_tracker.track_order(result, str(interaction.user.id), metadata)

                embed = await self._create_position_embed(
                    "✅ Take Profit Set",
                    f"**Symbol:** {normalized_symbol}\n"
                    f"**Position:** {side}\n"
                    f"**TP Price:** ${price:,.4f}\n"
                    f"**Amount:** {tp_amount:.6f} ({percentage}%)\n"
                    f"**Order ID:** {result.get('order_id', 'N/A')}"
                )
                await interaction.followup.send(embed=embed)
            else:
                embed = await self._create_position_embed(
                    "❌ Take Profit Failed",
                    f"Error: {result.get('error', 'Unknown error')}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Take profit command error: {e}")
            embed = await self._create_position_embed(
                "❌ Take Profit Error",
                f"An error occurred: {str(e)}",
                0xff4444
            )
            await interaction.followup.send(embed=embed)

    # ==================== STOP LOSS COMMAND ====================

    @app_commands.command(name="sl", description="Set stop loss order for position")
    @app_commands.describe(
        symbol="Trading symbol (e.g., btc, eth, btcusdt)",
        side="Position side (LONG/SHORT)",
        price="Stop loss price level",
        percentage="Percentage of position to close (default: 100%)"
    )
    async def stop_loss(
        self,
        interaction: discord.Interaction,
        symbol: str,
        side: str,
        price: float,
        percentage: Optional[float] = 100.0
    ):
        """Set stop loss order for position"""
        try:
            # Check channel
            if not self._check_trading_channel(interaction):
                await interaction.response.send_message("❌ This command can only be used in trading channels.", ephemeral=True)
                return

            # Validate inputs
            if side.upper() not in ['LONG', 'SHORT']:
                await interaction.response.send_message("❌ Side must be LONG or SHORT", ephemeral=True)
                return

            if not (0 < percentage <= 100):
                await interaction.response.send_message("❌ Percentage must be between 1-100", ephemeral=True)
                return

            await interaction.response.defer()

            # Normalize symbol
            normalized_symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Check if position exists
            positions = position_manager.get_user_positions(str(interaction.user.id), status='open')
            target_position = None

            for pos in positions:
                if pos['symbol'] == normalized_symbol and pos['side'] == side:
                    target_position = pos
                    break

            if not target_position:
                embed = await self._create_position_embed(
                    "❌ Stop Loss Failed",
                    f"No {side} position found for {normalized_symbol}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)
                return

            # Calculate SL amount
            position_size = abs(float(target_position['amount']))
            sl_amount = position_size * (percentage / 100.0)

            # Place stop loss order
            result = self.trading_service.place_stop_loss_order(
                symbol=normalized_symbol,
                side='sell' if side == 'LONG' else 'buy',
                amount=sl_amount,
                stop_price=price,
                position_side=side
            )

            if result['success']:
                # Track the SL order
                metadata = {
                    'symbol': normalized_symbol,
                    'side': 'sell' if side == 'LONG' else 'buy',
                    'amount': sl_amount,
                    'stop_price': price,
                    'order_type': 'stop_loss',
                    'position_side': side,
                    'percentage': percentage,
                    'command': 'sl'
                }

                order_tracker.track_order(result, str(interaction.user.id), metadata)

                embed = await self._create_position_embed(
                    "🛡️ Stop Loss Set",
                    f"**Symbol:** {normalized_symbol}\n"
                    f"**Position:** {side}\n"
                    f"**SL Price:** ${price:,.4f}\n"
                    f"**Amount:** {sl_amount:.6f} ({percentage}%)\n"
                    f"**Order ID:** {result.get('order_id', 'N/A')}"
                )
                await interaction.followup.send(embed=embed)
            else:
                embed = await self._create_position_embed(
                    "❌ Stop Loss Failed",
                    f"Error: {result.get('error', 'Unknown error')}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Stop loss command error: {e}")
            embed = await self._create_position_embed(
                "❌ Stop Loss Error",
                f"An error occurred: {str(e)}",
                0xff4444
            )
            await interaction.followup.send(embed=embed)

    # ==================== CLOSE ALL POSITIONS COMMAND ====================

    @app_commands.command(name="closeall", description="Close all open positions (REQUIRES CONFIRMATION)")
    @app_commands.describe(
        confirm="Type 'yes' to confirm closing ALL positions"
    )
    async def close_all_positions(
        self,
        interaction: discord.Interaction,
        confirm: str
    ):
        """Close all open positions with safety confirmation"""
        try:
            # Check channel
            if not self._check_trading_channel(interaction):
                await interaction.response.send_message("❌ This command can only be used in trading channels.", ephemeral=True)
                return

            # Safety confirmation check
            if confirm.lower() != 'yes':
                await interaction.response.send_message(
                    "❌ **SAFETY CHECK FAILED**\n"
                    "To close ALL positions, you must type: `/closeall confirm:yes`\n"
                    "⚠️ This action cannot be undone!",
                    ephemeral=True
                )
                return

            await interaction.response.defer()

            # Get all open positions for user
            positions = position_manager.get_user_positions(str(interaction.user.id), status='open')

            if not positions:
                embed = await self._create_position_embed(
                    "ℹ️ No Positions",
                    "You have no open positions to close.",
                    0x3498db
                )
                await interaction.followup.send(embed=embed)
                return

            # Close all positions using trading service
            result = self.trading_service.close_all_positions()

            if result['success']:
                closed_count = result.get('closed', 0)
                failed_count = result.get('failed', 0)

                embed = await self._create_position_embed(
                    "🔒 All Positions Closed",
                    f"**Closed:** {closed_count} positions\n"
                    f"**Failed:** {failed_count} positions\n"
                    f"**Total Positions:** {len(positions)}"
                )

                # Add position details
                if closed_count > 0:
                    position_list = []
                    for pos in positions[:5]:  # Show first 5
                        position_list.append(f"• {pos['symbol']} {pos['side']}")

                    if len(positions) > 5:
                        position_list.append(f"• ... and {len(positions) - 5} more")

                    embed.add_field(
                        name="Closed Positions",
                        value="\n".join(position_list),
                        inline=False
                    )

                await interaction.followup.send(embed=embed)
            else:
                embed = await self._create_position_embed(
                    "❌ Close All Failed",
                    f"Error: {result.get('error', 'Unknown error')}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Close all command error: {e}")
            embed = await self._create_position_embed(
                "❌ Close All Error",
                f"An error occurred: {str(e)}",
                0xff4444
            )
            await interaction.followup.send(embed=embed)

    # ==================== CLOSE SIDE POSITIONS COMMAND ====================

    @app_commands.command(name="closeside", description="Close all positions on one side (REQUIRES CONFIRMATION)")
    @app_commands.describe(
        side="Position side to close (LONG/SHORT)",
        confirm="Type 'yes' to confirm closing all positions on this side"
    )
    async def close_side_positions(
        self,
        interaction: discord.Interaction,
        side: str,
        confirm: str
    ):
        """Close all positions on one side with safety confirmation"""
        try:
            # Check channel
            if not self._check_trading_channel(interaction):
                await interaction.response.send_message("❌ This command can only be used in trading channels.", ephemeral=True)
                return

            # Validate side
            if side.upper() not in ['LONG', 'SHORT']:
                await interaction.response.send_message("❌ Side must be LONG or SHORT", ephemeral=True)
                return

            # Safety confirmation check
            if confirm.lower() != 'yes':
                await interaction.response.send_message(
                    f"❌ **SAFETY CHECK FAILED**\n"
                    f"To close ALL {side.upper()} positions, you must type: `/closeside side:{side} confirm:yes`\n"
                    f"⚠️ This action cannot be undone!",
                    ephemeral=True
                )
                return

            await interaction.response.defer()

            side = side.upper()

            # Get positions for this side
            all_positions = position_manager.get_user_positions(str(interaction.user.id), status='open')
            side_positions = [pos for pos in all_positions if pos['side'] == side]

            if not side_positions:
                embed = await self._create_position_embed(
                    "ℹ️ No Positions",
                    f"You have no open {side} positions to close.",
                    0x3498db
                )
                await interaction.followup.send(embed=embed)
                return

            # Close positions on this side
            result = self.trading_service.close_all_positions(side=side)

            if result['success']:
                closed_count = result.get('closed', 0)
                failed_count = result.get('failed', 0)

                embed = await self._create_position_embed(
                    f"🔒 All {side} Positions Closed",
                    f"**Closed:** {closed_count} positions\n"
                    f"**Failed:** {failed_count} positions\n"
                    f"**Total {side} Positions:** {len(side_positions)}"
                )

                # Add position details
                if closed_count > 0:
                    position_list = []
                    for pos in side_positions[:5]:  # Show first 5
                        position_list.append(f"• {pos['symbol']} {pos['side']}")

                    if len(side_positions) > 5:
                        position_list.append(f"• ... and {len(side_positions) - 5} more")

                    embed.add_field(
                        name=f"Closed {side} Positions",
                        value="\n".join(position_list),
                        inline=False
                    )

                await interaction.followup.send(embed=embed)
            else:
                embed = await self._create_position_embed(
                    f"❌ Close {side} Failed",
                    f"Error: {result.get('error', 'Unknown error')}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Close side command error: {e}")
            embed = await self._create_position_embed(
                "❌ Close Side Error",
                f"An error occurred: {str(e)}",
                0xff4444
            )
            await interaction.followup.send(embed=embed)

    # ==================== CLOSE SPECIFIC POSITION COMMAND ====================

    @app_commands.command(name="closepos", description="Close specific position with percentage control")
    @app_commands.describe(
        symbol="Trading symbol (e.g., btc, eth, btcusdt)",
        side="Position side (LONG/SHORT)",
        percentage="Percentage to close (25, 50, 75, 100)",
        confirm="Type 'yes' for 100% closes (optional for partial closes)"
    )
    async def close_position(
        self,
        interaction: discord.Interaction,
        symbol: str,
        side: str,
        percentage: float,
        confirm: Optional[str] = None
    ):
        """Close specific position with percentage control"""
        try:
            # Check channel
            if not self._check_trading_channel(interaction):
                await interaction.response.send_message("❌ This command can only be used in trading channels.", ephemeral=True)
                return

            # Validate inputs
            if side.upper() not in ['LONG', 'SHORT']:
                await interaction.response.send_message("❌ Side must be LONG or SHORT", ephemeral=True)
                return

            if not (0 < percentage <= 100):
                await interaction.response.send_message("❌ Percentage must be between 1-100", ephemeral=True)
                return

            # Safety confirmation for 100% closes
            if percentage == 100.0 and (not confirm or confirm.lower() != 'yes'):
                await interaction.response.send_message(
                    f"❌ **SAFETY CHECK REQUIRED**\n"
                    f"To close 100% of {side} {symbol}, you must add: `confirm:yes`\n"
                    f"Example: `/closepos symbol:{symbol} side:{side} percentage:100 confirm:yes`",
                    ephemeral=True
                )
                return

            await interaction.response.defer()

            # Normalize symbol
            normalized_symbol = smart_normalize_symbol(symbol)
            side = side.upper()

            # Check if position exists
            positions = position_manager.get_user_positions(str(interaction.user.id), status='open')
            target_position = None

            for pos in positions:
                if pos['symbol'] == normalized_symbol and pos['side'] == side:
                    target_position = pos
                    break

            if not target_position:
                embed = await self._create_position_embed(
                    "❌ Position Not Found",
                    f"No {side} position found for {normalized_symbol}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)
                return

            # Close position using trading service
            result = self.trading_service.close_position(
                symbol=normalized_symbol,
                side=side,
                percentage=percentage
            )

            if result['success']:
                position_size = abs(float(target_position['amount']))
                closed_amount = position_size * (percentage / 100.0)

                embed = await self._create_position_embed(
                    "🔒 Position Closed",
                    f"**Symbol:** {normalized_symbol}\n"
                    f"**Side:** {side}\n"
                    f"**Closed:** {closed_amount:.6f} ({percentage}%)\n"
                    f"**Remaining:** {position_size - closed_amount:.6f} ({100 - percentage}%)"
                )

                if percentage == 100.0:
                    embed.add_field(
                        name="Status",
                        value="✅ Position completely closed",
                        inline=False
                    )

                await interaction.followup.send(embed=embed)
            else:
                embed = await self._create_position_embed(
                    "❌ Close Position Failed",
                    f"Error: {result.get('error', 'Unknown error')}",
                    0xff4444
                )
                await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"❌ Close position command error: {e}")
            embed = await self._create_position_embed(
                "❌ Close Position Error",
                f"An error occurred: {str(e)}",
                0xff4444
            )
            await interaction.followup.send(embed=embed)

async def setup(bot):
    await bot.add_cog(PositionControlCommands(bot))
