#!/usr/bin/env python3
"""
Simplified Real-time Monitor for Binance Futures
Handles WebSocket connections and data streaming without overlapping functionality
Focuses on connection management and event dispatching
"""

import asyncio
import json
import logging
import websockets
import hmac
import hashlib
import time
from typing import Dict, List, Optional, Callable, Set
from datetime import datetime, timezone

from utils.config import get_binance_api_key, get_binance_api_secret

logger = logging.getLogger(__name__)

class RealtimeMonitor:
    """
    Simplified real-time monitor that handles:
    1. WebSocket connection management with auto-reconnect
    2. User data stream for orders/positions
    3. Market data stream for price updates
    4. Event dispatching to registered handlers
    5. Connection health monitoring with ping/pong
    """

    def __init__(self):
        # Connection settings
        self.base_url = "wss://fstream.binance.com"
        self.api_key = get_binance_api_key()
        self.api_secret = get_binance_api_secret()
        
        # Connection state
        self.running = False
        self.user_websocket = None
        self.market_websocket = None
        self.listen_key = None
        
        # Health monitoring
        self.connection_start_time = None
        self.last_ping_time = None
        self.ping_interval = 180  # 3 minutes (Binance requires < 24 hours)
        self.ping_task = None
        
        # Reconnection settings
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5  # seconds
        
        # Event handlers
        self.order_handlers: List[Callable] = []
        self.position_handlers: List[Callable] = []
        self.price_handlers: List[Callable] = []
        
        # Tracked symbols for market data
        self.tracked_symbols: Set[str] = set()
        
        # Statistics
        self.stats = {
            'messages_received': 0,
            'order_updates': 0,
            'position_updates': 0,
            'price_updates': 0,
            'reconnections': 0,
            'errors': 0
        }
        
        logger.info("🌐 Realtime Monitor initialized")

    # ==================== CONNECTION MANAGEMENT ====================

    async def start(self) -> bool:
        """Start real-time monitoring"""
        try:
            logger.info("🚀 Starting Binance real-time monitoring...")
            
            # Get listen key for user data stream
            listen_key = await self._get_listen_key()
            if not listen_key:
                logger.error("❌ Failed to get listen key")
                return False
            
            self.listen_key = listen_key
            self.running = True
            self.connection_start_time = time.time()
            
            # Start ping task for listen key maintenance
            self.ping_task = asyncio.create_task(self._ping_loop())
            
            # Start connection tasks
            user_task = asyncio.create_task(self._connect_user_stream())
            market_task = asyncio.create_task(self._connect_market_stream())
            
            # Wait for both to start
            results = await asyncio.gather(user_task, market_task, return_exceptions=True)
            
            # Check if any failed
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"❌ Connection failed: {result}")
                    return False
            
            logger.info("✅ Real-time monitoring started successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start real-time monitoring: {e}")
            return False

    async def stop(self):
        """Stop real-time monitoring"""
        try:
            logger.info("🛑 Stopping real-time monitoring...")
            self.running = False
            
            # Cancel ping task
            if self.ping_task:
                self.ping_task.cancel()
                try:
                    await self.ping_task
                except asyncio.CancelledError:
                    pass
            
            # Close WebSocket connections
            if self.user_websocket:
                await self.user_websocket.close()
            if self.market_websocket:
                await self.market_websocket.close()
            
            # Delete listen key
            if self.listen_key:
                await self._delete_listen_key()
            
            logger.info("✅ Real-time monitoring stopped")
            
        except Exception as e:
            logger.error(f"❌ Error stopping real-time monitoring: {e}")

    async def _get_listen_key(self) -> Optional[str]:
        """Get listen key for user data stream"""
        try:
            import aiohttp
            
            url = "https://fapi.binance.com/fapi/v1/listenKey"
            headers = {"X-MBX-APIKEY": self.api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        listen_key = data.get('listenKey')
                        logger.info("✅ Listen key obtained successfully")
                        return listen_key
                    else:
                        logger.error(f"❌ Failed to get listen key: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ Error getting listen key: {e}")
            return None

    async def _delete_listen_key(self):
        """Delete listen key"""
        try:
            import aiohttp
            
            url = "https://fapi.binance.com/fapi/v1/listenKey"
            headers = {"X-MBX-APIKEY": self.api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.delete(url, headers=headers) as response:
                    if response.status == 200:
                        logger.info("✅ Listen key deleted successfully")
                    else:
                        logger.warning(f"⚠️ Failed to delete listen key: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ Error deleting listen key: {e}")

    async def _ping_loop(self):
        """Maintain listen key with periodic pings"""
        while self.running:
            try:
                await asyncio.sleep(self.ping_interval)
                if self.running:
                    await self._ping_listen_key()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in ping loop: {e}")

    async def _ping_listen_key(self):
        """Ping listen key to keep it alive"""
        try:
            import aiohttp
            
            url = "https://fapi.binance.com/fapi/v1/listenKey"
            headers = {"X-MBX-APIKEY": self.api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.put(url, headers=headers) as response:
                    if response.status == 200:
                        self.last_ping_time = time.time()
                        logger.debug("📡 Listen key pinged successfully")
                    else:
                        logger.warning(f"⚠️ Failed to ping listen key: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ Error pinging listen key: {e}")

    # ==================== USER DATA STREAM ====================

    async def _connect_user_stream(self):
        """Connect to User Data Stream for orders/positions"""
        while self.running:
            try:
                ws_url = f"{self.base_url}/ws/{self.listen_key}"
                logger.info("🔌 Connecting to User Data Stream...")
                
                async with websockets.connect(ws_url, ping_interval=None) as websocket:
                    self.user_websocket = websocket
                    self.reconnect_attempts = 0
                    logger.info("✅ User Data Stream connected")
                    
                    # Notify handlers of connection
                    await self._notify_connection_status(True)
                    
                    # Listen for messages
                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_user_message(message)
                        
            except websockets.exceptions.ConnectionClosed:
                if self.running:
                    logger.warning("⚠️ User Data Stream disconnected")
                    await self._notify_connection_status(False)
                    await self._handle_reconnection("user")
            except Exception as e:
                logger.error(f"❌ User Data Stream error: {e}")
                self.stats['errors'] += 1
                if self.running:
                    await self._handle_reconnection("user")

    async def _handle_user_message(self, message: str):
        """Handle User Data Stream messages"""
        try:
            self.stats['messages_received'] += 1
            data = json.loads(message)
            
            event_type = data.get('e')
            
            if event_type == 'ORDER_TRADE_UPDATE':
                await self._handle_order_update(data)
            elif event_type == 'ACCOUNT_UPDATE':
                await self._handle_account_update(data)
            else:
                logger.debug(f"📨 Unhandled user event type: {event_type}")
                
        except json.JSONDecodeError:
            logger.error(f"❌ Failed to parse user message: {message}")
        except Exception as e:
            logger.error(f"❌ Error handling user message: {e}")

    async def _handle_order_update(self, data: Dict):
        """Handle order update events"""
        try:
            self.stats['order_updates'] += 1
            order_data = data.get('o', {})
            
            # Dispatch to registered order handlers
            for handler in self.order_handlers:
                try:
                    await handler(order_data)
                except Exception as e:
                    logger.error(f"❌ Error in order handler: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling order update: {e}")

    async def _handle_account_update(self, data: Dict):
        """Handle account/position update events"""
        try:
            self.stats['position_updates'] += 1
            account_data = data.get('a', {})
            
            # Dispatch to registered position handlers
            for handler in self.position_handlers:
                try:
                    await handler(account_data)
                except Exception as e:
                    logger.error(f"❌ Error in position handler: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling account update: {e}")

    # ==================== MARKET DATA STREAM ====================

    async def _connect_market_stream(self):
        """Connect to Market Data Stream for price updates"""
        while self.running:
            try:
                if not self.tracked_symbols:
                    await asyncio.sleep(5)  # Wait for symbols to be added
                    continue
                
                # Create stream names for tracked symbols
                streams = [f"{symbol.lower()}@ticker" for symbol in self.tracked_symbols]
                stream_names = "/".join(streams)
                ws_url = f"{self.base_url}/stream?streams={stream_names}"
                
                logger.info(f"🔌 Connecting to Market Data Stream: {len(self.tracked_symbols)} symbols")
                
                async with websockets.connect(ws_url, ping_interval=None) as websocket:
                    self.market_websocket = websocket
                    logger.info("✅ Market Data Stream connected")
                    
                    # Listen for messages
                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_market_message(message)
                        
            except websockets.exceptions.ConnectionClosed:
                if self.running:
                    logger.warning("⚠️ Market Data Stream disconnected")
                    await self._handle_reconnection("market")
            except Exception as e:
                logger.error(f"❌ Market Data Stream error: {e}")
                self.stats['errors'] += 1
                if self.running:
                    await self._handle_reconnection("market")

    async def _handle_market_message(self, message: str):
        """Handle Market Data Stream messages"""
        try:
            data = json.loads(message)
            
            # Market data comes wrapped in stream format
            if 'stream' in data and 'data' in data:
                stream_data = data['data']
                event_type = stream_data.get('e')
                
                if event_type == '24hrTicker':
                    await self._handle_price_update(stream_data)
                else:
                    logger.debug(f"📨 Unhandled market event type: {event_type}")
                    
        except json.JSONDecodeError:
            logger.error(f"❌ Failed to parse market message: {message}")
        except Exception as e:
            logger.error(f"❌ Error handling market message: {e}")

    async def _handle_price_update(self, data: Dict):
        """Handle price update events"""
        try:
            self.stats['price_updates'] += 1
            
            # Dispatch to registered price handlers
            for handler in self.price_handlers:
                try:
                    await handler(data)
                except Exception as e:
                    logger.error(f"❌ Error in price handler: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling price update: {e}")

    # ==================== RECONNECTION HANDLING ====================

    async def _handle_reconnection(self, stream_type: str):
        """Handle reconnection logic"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"❌ Max reconnection attempts reached for {stream_type} stream")
            return
        
        self.reconnect_attempts += 1
        self.stats['reconnections'] += 1
        
        delay = min(self.reconnect_delay * self.reconnect_attempts, 60)  # Max 60 seconds
        logger.info(f"🔄 Reconnecting {stream_type} stream in {delay} seconds (attempt {self.reconnect_attempts})")
        
        await asyncio.sleep(delay)

    async def _notify_connection_status(self, connected: bool):
        """Notify unified tracker of connection status"""
        try:
            from services.trading.order_tracker import order_tracker
            order_tracker.set_websocket_status(connected)
        except Exception as e:
            logger.error(f"❌ Error notifying connection status: {e}")

    # ==================== HANDLER REGISTRATION ====================

    def register_order_handler(self, handler: Callable):
        """Register handler for order updates"""
        self.order_handlers.append(handler)
        logger.info(f"📝 Registered order handler: {handler.__name__}")

    def register_position_handler(self, handler: Callable):
        """Register handler for position updates"""
        self.position_handlers.append(handler)
        logger.info(f"📝 Registered position handler: {handler.__name__}")

    def register_price_handler(self, handler: Callable):
        """Register handler for price updates"""
        self.price_handlers.append(handler)
        logger.info(f"📝 Registered price handler: {handler.__name__}")

    def add_tracked_symbol(self, symbol: str):
        """Add symbol to market data tracking"""
        self.tracked_symbols.add(symbol.upper())
        logger.info(f"📊 Added symbol to tracking: {symbol}")

    def remove_tracked_symbol(self, symbol: str):
        """Remove symbol from market data tracking"""
        self.tracked_symbols.discard(symbol.upper())
        logger.info(f"📊 Removed symbol from tracking: {symbol}")

    def get_status(self) -> Dict:
        """Get monitor status"""
        return {
            'running': self.running,
            'user_connected': self.user_websocket is not None,
            'market_connected': self.market_websocket is not None,
            'tracked_symbols': len(self.tracked_symbols),
            'reconnect_attempts': self.reconnect_attempts,
            'uptime': time.time() - self.connection_start_time if self.connection_start_time else 0,
            'last_ping': self.last_ping_time,
            'stats': self.stats.copy()
        }

# Global realtime monitor instance
realtime_monitor = RealtimeMonitor()
