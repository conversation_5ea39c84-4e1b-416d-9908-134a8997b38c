import logging
import ccxt
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from utils.config import get_binance_credentials, load_config
from services.core.error_service import handle_service_errors, retry_with_backoff, NetworkError, TimeoutError
from services.core.symbol_service import format_symbol_for_exchange
from services.data.cache_service import get_cache

logger = logging.getLogger(__name__)

class BinanceFuturesTrading:
    """
    Core Binance Futures Trading Engine

    Responsibilities:
    - Order placement and cancellation (market, limit, stop, take profit)
    - Position queries and basic operations
    - Account balance and margin information
    - Direct Binance API interactions only

    Does NOT handle:
    - Order tracking (handled by order_tracker)
    - Notifications (handled by notification_service)
    - WebSocket connections (handled by realtime_monitor)
    - Risk management (handled by risk_manager)
    """

    def __init__(self):
        self.exchange = None
        self.balance_cache = get_cache("trading_balance", max_size=10, ttl=30)
        self.position_cache = get_cache("positions", max_size=50, ttl=10)
        self.connection_healthy = True
        self._initialize_exchange()

    def _initialize_exchange(self):
        """Initialize Binance Futures exchange"""
        try:
            credentials = get_binance_credentials()
            config = load_config()

            api_key = credentials.get('api_key', '')
            api_secret = credentials.get('api_secret', '')
            use_testnet = config.get('binance', {}).get('testnet', False)

            logger.info(f"Initializing Binance exchange with testnet: {use_testnet}")
            logger.info(f"API Key length: {len(api_key) if api_key else 0}")
            logger.info(f"API Secret length: {len(api_secret) if api_secret else 0}")

            if not api_key or not api_secret:
                raise ValueError("Binance API credentials not found in config")

            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'timeout': 30000,
                'enableRateLimit': True,
                'sandbox': use_testnet,
                'options': {
                    'defaultType': 'future'
                }
            })

            logger.info("Loading markets...")
            self.exchange.load_markets()
            logger.info(f"Binance Futures initialized successfully (testnet: {use_testnet})")
            logger.info(f"Available markets: {len(self.exchange.markets)}")

        except Exception as e:
            logger.error(f"Failed to initialize Binance Futures: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def get_account_balance(self, use_cache: bool = True) -> Dict[str, Any]:
        """Get account balance with caching and fallback"""
        if use_cache:
            cached_balance = self.balance_cache.get("balance")
            if cached_balance:
                logger.debug("Using cached balance data")
                return cached_balance

        try:
            return self._fetch_fresh_balance()
        except Exception as e:
            logger.error(f"Failed to fetch fresh balance: {e}")

            cached_balance = self.balance_cache.get("balance")
            if cached_balance:
                logger.warning("Using stale cached balance due to API error")
                cached_balance['stale'] = True
                return cached_balance

            return self._get_fallback_balance(str(e))

    @handle_service_errors
    @retry_with_backoff(max_retries=2, initial_backoff=1.0, max_backoff=5.0,
                       exception_types=[NetworkError, TimeoutError, ccxt.NetworkError, ccxt.ExchangeNotAvailable])
    def _fetch_fresh_balance(self) -> Dict[str, Any]:
        """Fetch fresh balance from API"""
        try:
            logger.debug("Fetching fresh account balance from Binance...")
            balance = self.exchange.fetch_balance()

            result = {
                'success': True,
                'free_usdt': balance.get('USDT', {}).get('free', 0),
                'total_usdt': balance.get('USDT', {}).get('total', 0),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'stale': False
            }

            self.balance_cache.set("balance", result)
            self.last_balance_fetch = datetime.now(timezone.utc)
            self.connection_healthy = True

            logger.debug(f"Balance fetch successful: USDT free={result['free_usdt']}")
            return result

        except ccxt.AuthenticationError as e:
            logger.error(f"Binance authentication error: {e}")
            self.connection_healthy = False
            return {'success': False, 'error': f'Authentication failed: {str(e)}'}
        except ccxt.PermissionDenied as e:
            logger.error(f"Binance permission denied: {e}")
            self.connection_healthy = False
            return {'success': False, 'error': f'Permission denied: {str(e)}'}
        except ccxt.DDoSProtection as e:
            logger.warning(f"Rate limit hit when fetching balance: {e}")
            time.sleep(2)
            raise NetworkError(f"Rate limit exceeded: {e}", e)
        except ccxt.NetworkError as e:
            logger.error(f"Network error fetching balance: {e}")
            self.connection_healthy = False
            raise NetworkError(f"Network connection issue: {e}", e)
        except ccxt.ExchangeNotAvailable as e:
            logger.error(f"Binance exchange not available: {e}")
            self.connection_healthy = False
            raise NetworkError(f"Exchange unavailable: {e}", e)
        except ccxt.BaseError as e:
            logger.error(f"CCXT base error fetching balance: {e}")
            self.connection_healthy = False
            return {'success': False, 'error': f'Exchange error: {str(e)}'}
        except Exception as e:
            logger.error(f"Unexpected error fetching balance: {e}")
            self.connection_healthy = False
            return {'success': False, 'error': f'Unexpected error: {str(e)}'}

    def _get_fallback_balance(self, error_msg: str) -> Dict[str, Any]:
        """Return fallback balance when API is unavailable"""
        return {
            'success': False,
            'free_usdt': 0,
            'total_usdt': 0,
            'error': f'API unavailable: {error_msg}',
            'fallback': True,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection health status"""
        return {
            'healthy': self.connection_healthy,
            'last_fetch': self.last_balance_fetch.isoformat() if self.last_balance_fetch else None,
            'cache_size': self.balance_cache.size()
        }

    @handle_service_errors
    def place_market_order(self, symbol: str, side: str, amount: float,
                          position_side: str = None) -> Dict[str, Any]:
        """
        Place market order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_market_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                params={'positionSide': position_side}
            )

            logger.info(f"Market {side}: {amount} {symbol} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Market order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_limit_order(self, symbol: str, side: str, amount: float, price: float,
                         position_side: str = None) -> Dict[str, Any]:
        """
        Place limit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            price: Limit price
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_limit_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                price=price,
                params={'positionSide': position_side}
            )

            logger.info(f"Limit {side}: {amount} {symbol} @ {price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_stop_loss_order(self, symbol: str, side: str, amount: float, stop_price: float,
                             position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place stop loss order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Stop price to trigger
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }
            
            if reduce_only:
                if (position_side == 'LONG' and side == 'sell') or (position_side == 'SHORT' and side == 'buy'):
                    params['reduceOnly'] = True
            
            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='STOP_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Stop loss: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Stop loss error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_take_profit_order(self, symbol: str, side: str, amount: float, stop_price: float,
                               position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place take profit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Price to trigger take profit
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only (ignored for take profit)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }

            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='TAKE_PROFIT_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Take profit: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Take profit error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Cancel order"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            self.exchange.cancel_order(order_id, formatted_symbol)

            logger.info(f"Cancelled order: {order_id}")
            return {'success': True}

        except Exception as e:
            logger.error(f"Cancel order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status from Binance"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            order = self.exchange.fetch_order(order_id, formatted_symbol)

            return {
                'success': True,
                'order': {
                    'id': order.get('id'),
                    'symbol': order.get('symbol'),
                    'status': order.get('status'),
                    'side': order.get('side'),
                    'amount': order.get('amount'),
                    'price': order.get('price'),
                    'avgPrice': order.get('average'),
                    'executedQty': order.get('filled'),
                    'timestamp': order.get('timestamp')
                }
            }

        except Exception as e:
            logger.error(f"Get order status error: {e}")
            return {'success': False, 'error': str(e)}

    # ==================== POSITION CONTROL METHODS ====================

    @handle_service_errors
    def get_positions(self, symbol: str = None) -> Dict[str, Any]:
        """Get current positions from Binance"""
        try:
            cache_key = f"positions_{symbol}" if symbol else "positions_all"
            cached = self.position_cache.get(cache_key)
            if cached:
                return cached

            if symbol:
                formatted_symbol = format_symbol_for_exchange(symbol)
                positions = self.exchange.fetch_positions([formatted_symbol])
            else:
                positions = self.exchange.fetch_positions()

            # Filter only positions with size > 0
            active_positions = [pos for pos in positions if float(pos.get('contracts', 0)) != 0]

            result = {
                'success': True,
                'positions': active_positions,
                'count': len(active_positions)
            }

            self.position_cache.set(cache_key, result)
            return result

        except Exception as e:
            logger.error(f"Get positions error: {e}")
            return {'success': False, 'error': str(e), 'positions': []}

    @handle_service_errors
    def close_position(self, symbol: str, side: str, percentage: float = 100.0) -> Dict[str, Any]:
        """
        Close position with specified percentage

        Args:
            symbol: Trading symbol
            side: Position side ('LONG' or 'SHORT')
            percentage: Percentage to close (25, 50, 75, 100)
        """
        try:
            # Get current position
            positions_result = self.get_positions(symbol)
            if not positions_result['success']:
                return positions_result

            target_position = None
            for pos in positions_result['positions']:
                if pos['symbol'] == format_symbol_for_exchange(symbol) and pos['side'] == side:
                    target_position = pos
                    break

            if not target_position:
                return {'success': False, 'error': f'No {side} position found for {symbol}'}

            # Calculate close amount
            position_size = abs(float(target_position['contracts']))
            close_amount = position_size * (percentage / 100.0)

            # Determine order side (opposite of position)
            order_side = 'sell' if side == 'LONG' else 'buy'

            # Place market order to close
            result = self.place_market_order(
                symbol=symbol,
                side=order_side,
                amount=close_amount,
                position_side=side
            )

            if result['success']:
                logger.info(f"🔒 Closed {percentage}% of {side} {symbol}: {close_amount}")

            return result

        except Exception as e:
            logger.error(f"Close position error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def close_all_positions(self, side: str = None) -> Dict[str, Any]:
        """
        Close all positions or all positions on one side

        Args:
            side: Optional side filter ('LONG' or 'SHORT')
        """
        try:
            positions_result = self.get_positions()
            if not positions_result['success']:
                return positions_result

            positions = positions_result['positions']
            if side:
                positions = [pos for pos in positions if pos['side'] == side]

            if not positions:
                return {'success': True, 'message': 'No positions to close', 'closed': 0}

            closed_count = 0
            failed_count = 0
            results = []

            for position in positions:
                symbol = position['symbol']
                pos_side = position['side']
                amount = abs(float(position['contracts']))

                # Determine order side (opposite of position)
                order_side = 'sell' if pos_side == 'LONG' else 'buy'

                # Close position
                result = self.place_market_order(
                    symbol=symbol,
                    side=order_side,
                    amount=amount,
                    position_side=pos_side
                )

                if result['success']:
                    closed_count += 1
                    logger.info(f"🔒 Closed {pos_side} {symbol}: {amount}")
                else:
                    failed_count += 1
                    logger.error(f"❌ Failed to close {pos_side} {symbol}: {result.get('error')}")

                results.append({
                    'symbol': symbol,
                    'side': pos_side,
                    'amount': amount,
                    'success': result['success'],
                    'error': result.get('error')
                })

            return {
                'success': True,
                'closed': closed_count,
                'failed': failed_count,
                'results': results
            }

        except Exception as e:
            logger.error(f"Close all positions error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_open_orders(self, symbol: str = None) -> Dict[str, Any]:
        """Get open orders"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol) if symbol else None
            orders = self.exchange.fetch_open_orders(formatted_symbol)

            return {
                'success': True,
                'orders': orders,
                'count': len(orders)
            }

        except Exception as e:
            logger.error(f"Get orders error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            order = self.exchange.fetch_order(order_id, formatted_symbol)

            return {
                'success': True,
                'status': order.get('status'),
                'filled': order.get('filled', 0)
            }

        except Exception as e:
            logger.error(f"Get order status error: {e}")
            return {'success': False, 'error': str(e)}
