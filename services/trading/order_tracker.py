#!/usr/bin/env python3
"""
Order Tracking System
Provides a single source of truth for order tracking and management
Handles order lifecycle, real-time monitoring, and position updates
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set
from services.data.database import trading_db
from services.trading.trading_service import BinanceFuturesTrading

logger = logging.getLogger(__name__)

class OrderTracker:
    """
    Order tracking system that provides:
    1. Order lifecycle management (creation, updates, fills)
    2. Real-time status monitoring with WebSocket integration
    3. API polling backup for reliability
    4. Order reconciliation and consistency checks
    5. Position update triggers
    """

    def __init__(self, trading_service: BinanceFuturesTrading = None):
        self.trading_service = trading_service
        self.db = trading_db

        # Tracking state
        self.pending_orders: Set[str] = set()
        self.tracked_symbols: Set[str] = set()

        # Backup polling configuration
        self.backup_polling_enabled = False
        self.backup_polling_interval = 30  # seconds
        self.backup_task: Optional[asyncio.Task] = None

        # Reconciliation settings
        self.last_reconciliation: Optional[datetime] = None
        self.reconciliation_interval = 300  # 5 minutes
        self.reconciliation_task: Optional[asyncio.Task] = None

        # Connection status
        self.websocket_connected = False

        logger.info("🔄 Unified Order Tracker initialized")

    # ==================== CORE ORDER TRACKING ====================

    def track_order(self, order_result: Dict, user_id: str, metadata: Dict = None) -> bool:
        """
        Start tracking an order after it's placed

        Args:
            order_result: Result from trading service order placement
            user_id: Discord user ID
            metadata: Additional order metadata (command info, etc.)
        """
        try:
            if not order_result.get('success') or not order_result.get('order_id'):
                logger.error("❌ Cannot track order: invalid order result")
                return False

            order_id = str(order_result['order_id'])

            # Prepare order data for database
            order_data = {
                'order_id': order_id,
                'user_id': user_id,
                'symbol': metadata.get('symbol', '') if metadata else '',
                'side': metadata.get('side', '') if metadata else '',
                'amount': metadata.get('amount', 0.0) if metadata else 0.0,
                'price': metadata.get('price', 0.0) if metadata else 0.0,
                'order_type': metadata.get('order_type', 'market') if metadata else 'market',
                'status': 'open',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'metadata': metadata or {}
            }

            # Save to database
            success = self.db.create_order(order_data)
            if success:
                self.pending_orders.add(order_id)
                if order_data['symbol']:
                    self.tracked_symbols.add(order_data['symbol'])

                logger.info(f"✅ Started tracking order: {order_id}")

                # Track TP/SL orders if they exist
                self._track_conditional_orders(order_result, user_id, metadata)

                # Start backup polling if WebSocket is down
                if not self.websocket_connected:
                    asyncio.create_task(self._ensure_backup_polling())

            return success

        except Exception as e:
            logger.error(f"❌ Failed to track order: {e}")
            return False

    def _track_conditional_orders(self, order_result: Dict, user_id: str, metadata: Dict = None):
        """Track TP/SL orders if they exist"""
        try:
            if order_result.get('tp_order_id'):
                self._track_tp_sl_order(order_result['tp_order_id'], user_id, 'take_profit', metadata)

            if order_result.get('sl_order_id'):
                self._track_tp_sl_order(order_result['sl_order_id'], user_id, 'stop_loss', metadata)

        except Exception as e:
            logger.error(f"❌ Failed to track conditional orders: {e}")

    def _track_tp_sl_order(self, order_id: str, user_id: str, order_type: str, metadata: Dict = None):
        """Track take profit or stop loss order"""
        try:
            tp_sl_data = {
                'order_id': order_id,
                'user_id': user_id,
                'symbol': metadata.get('symbol', '') if metadata else '',
                'side': metadata.get('side', '') if metadata else '',
                'amount': metadata.get('amount', 0.0) if metadata else 0.0,
                'price': metadata.get('tp_price' if order_type == 'take_profit' else 'sl_price', 0.0) if metadata else 0.0,
                'order_type': order_type,
                'status': 'open',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'metadata': metadata or {}
            }

            self.db.create_order(tp_sl_data)
            self.pending_orders.add(order_id)
            logger.info(f"✅ Started tracking {order_type} order: {order_id}")

        except Exception as e:
            logger.error(f"❌ Failed to track {order_type} order: {e}")

    # ==================== ORDER STATUS UPDATES ====================

    def update_order_status(self, order_id: str, new_status: str, fill_data: Dict = None) -> bool:
        """
        Update order status when status changes

        Args:
            order_id: Binance order ID
            new_status: New status (filled, cancelled, rejected, etc.)
            fill_data: Fill information if order was filled
        """
        try:
            filled_data = None
            if new_status == 'filled' and fill_data:
                filled_data = {
                    'filled_at': datetime.now(timezone.utc).isoformat(),
                    'filled_price': fill_data.get('price', 0.0),
                    'filled_amount': fill_data.get('amount', 0.0),
                    'commission': fill_data.get('commission', 0.0),
                    'commission_asset': fill_data.get('commission_asset', 'USDT')
                }

            success = self.db.update_order_status(order_id, new_status, filled_data)

            if success:
                logger.info(f"✅ Order status updated: {order_id} -> {new_status}")

                # Remove from pending if completed
                if new_status in ['filled', 'cancelled', 'rejected', 'expired']:
                    self.pending_orders.discard(order_id)

                # Trigger position update if filled
                if new_status == 'filled':
                    self._handle_order_fill(order_id, fill_data)

            return success

        except Exception as e:
            logger.error(f"❌ Failed to update order status: {e}")
            return False

    def _handle_order_fill(self, order_id: str, fill_data: Dict = None):
        """Handle order fill - trigger position manager update"""
        try:
            # Get order details from database
            order = self.db.get_order(order_id)
            if not order:
                logger.error(f"❌ Order not found for fill handling: {order_id}")
                return

            # Import here to avoid circular dependency
            from services.trading.position_manager import position_manager
            from services.trading.notification_service import notification_service

            # Update position
            position_updated = position_manager.update_position_from_fill(order, fill_data or {})

            if position_updated:
                logger.info(f"📊 Position updated for order fill: {order_id}")

                # Send notification
                asyncio.create_task(
                    notification_service.send_order_notification(
                        order['user_id'], 'order_filled', order
                    )
                )
            else:
                logger.warning(f"⚠️ Failed to update position for order: {order_id}")

        except Exception as e:
            logger.error(f"❌ Failed to handle order fill: {e}")

    # ==================== WEBSOCKET INTEGRATION ====================

    def set_websocket_status(self, connected: bool):
        """Update WebSocket connection status"""
        self.websocket_connected = connected
        logger.info(f"🔌 WebSocket status: {'Connected' if connected else 'Disconnected'}")

        if not connected:
            # Start backup polling when WebSocket disconnects
            asyncio.create_task(self._ensure_backup_polling())
        else:
            # Stop backup polling when WebSocket reconnects
            asyncio.create_task(self._stop_backup_polling())

    async def handle_websocket_order_update(self, order_data: Dict):
        """Handle order update from WebSocket"""
        try:
            order_id = str(order_data.get('i', ''))  # Binance order ID field
            status = order_data.get('X', '').lower()  # Order status

            if not order_id or order_id not in self.pending_orders:
                return  # Not tracking this order

            # Map Binance status to our status
            status_mapping = {
                'new': 'open',
                'partially_filled': 'partially_filled',
                'filled': 'filled',
                'canceled': 'cancelled',
                'rejected': 'rejected',
                'expired': 'expired'
            }

            mapped_status = status_mapping.get(status, status)

            # Prepare fill data if order is filled
            fill_data = None
            if mapped_status in ['filled', 'partially_filled']:
                fill_data = {
                    'price': float(order_data.get('L', 0)),  # Last executed price
                    'amount': float(order_data.get('l', 0)),  # Last executed quantity
                    'commission': float(order_data.get('n', 0)),  # Commission
                    'commission_asset': order_data.get('N', 'USDT')  # Commission asset
                }

            # Update order status
            self.update_order_status(order_id, mapped_status, fill_data)

        except Exception as e:
            logger.error(f"❌ Error handling WebSocket order update: {e}")

    # ==================== BACKUP POLLING SYSTEM ====================

    async def _ensure_backup_polling(self):
        """Ensure backup polling is running"""
        if not self.backup_polling_enabled and self.pending_orders:
            await self.start_backup_polling()

    async def start_backup_polling(self):
        """Start backup API polling for order status"""
        if self.backup_polling_enabled:
            return

        self.backup_polling_enabled = True
        self.backup_task = asyncio.create_task(self._backup_polling_loop())
        logger.info("🔄 Started backup order polling")

    async def _stop_backup_polling(self):
        """Stop backup polling when WebSocket is healthy"""
        if self.backup_polling_enabled:
            self.backup_polling_enabled = False
            if self.backup_task:
                self.backup_task.cancel()
                try:
                    await self.backup_task
                except asyncio.CancelledError:
                    pass
            logger.info("⏹️ Stopped backup order polling")

    async def _backup_polling_loop(self):
        """Backup polling loop for order status"""
        while self.backup_polling_enabled:
            try:
                if self.pending_orders:
                    await self._poll_pending_orders()
                await asyncio.sleep(self.backup_polling_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in backup polling: {e}")
                await asyncio.sleep(5)

    async def _poll_pending_orders(self):
        """Poll pending orders for status updates"""
        try:
            if not self.trading_service:
                return

            # Get pending orders from database
            pending_orders = list(self.pending_orders)

            for order_id in pending_orders:
                try:
                    # Get order from database to get symbol
                    order = self.db.get_order(order_id)
                    if not order:
                        self.pending_orders.discard(order_id)
                        continue

                    # Check order status via API
                    api_status = await self._get_order_status_from_api(order_id, order['symbol'])
                    if api_status:
                        await self._process_api_order_update(order, api_status)

                except Exception as e:
                    logger.error(f"❌ Error polling order {order_id}: {e}")

        except Exception as e:
            logger.error(f"❌ Error in poll pending orders: {e}")

    async def _get_order_status_from_api(self, order_id: str, symbol: str) -> Optional[Dict]:
        """Get order status from Binance API"""
        try:
            if not self.trading_service:
                return None

            # Use trading service to get order status
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.trading_service.get_order_status, symbol, order_id
            )

            return result if result.get('success') else None

        except Exception as e:
            logger.error(f"❌ Error getting order status from API: {e}")
            return None

    async def _process_api_order_update(self, order: Dict, api_status: Dict):
        """Process order update from API"""
        try:
            api_order = api_status.get('order', {})
            new_status = api_order.get('status', '').lower()

            # Map API status to our status
            status_mapping = {
                'new': 'open',
                'partially_filled': 'partially_filled',
                'filled': 'filled',
                'canceled': 'cancelled',
                'rejected': 'rejected',
                'expired': 'expired'
            }

            mapped_status = status_mapping.get(new_status, new_status)

            # Check if status changed
            if order['status'] != mapped_status:
                fill_data = None
                if mapped_status in ['filled', 'partially_filled']:
                    fill_data = {
                        'price': float(api_order.get('avgPrice', 0)),
                        'amount': float(api_order.get('executedQty', 0)),
                        'commission': 0.0,  # API doesn't provide commission in order status
                        'commission_asset': 'USDT'
                    }

                self.update_order_status(order['order_id'], mapped_status, fill_data)

        except Exception as e:
            logger.error(f"❌ Error processing API order update: {e}")

    # ==================== ORDER RECONCILIATION ====================

    async def start_reconciliation(self):
        """Start periodic order reconciliation"""
        if self.reconciliation_task:
            return

        self.reconciliation_task = asyncio.create_task(self._reconciliation_loop())
        logger.info("🔄 Started order reconciliation")

    async def _reconciliation_loop(self):
        """Periodic reconciliation loop"""
        while True:
            try:
                await asyncio.sleep(self.reconciliation_interval)
                await self._reconcile_orders()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in reconciliation loop: {e}")
                
    async def force_reconcile_orders(self, user_id: str = None) -> Dict[str, Any]:
        """Force immediate reconciliation of orders
        
        Args:
            user_id: Optional user ID to reconcile orders for a specific user only
            
        Returns:
            Dictionary with reconciliation results
        """
        try:
            if not self.trading_service:
                return {
                    'success': False,
                    'error': 'Trading service not initialized'
                }
                
            # Kiểm tra thời gian từ lần đối chiếu cuối cùng để tránh gọi API quá nhiều
            if self.last_reconciliation:
                time_since_last = (datetime.now(timezone.utc) - self.last_reconciliation).total_seconds()
                if time_since_last < 10:  # Giới hạn tối thiểu 10 giây giữa các lần đối chiếu
                    return {
                        'success': False,
                        'error': f'Rate limited: Last reconciliation was {time_since_last:.1f} seconds ago',
                        'last_reconciliation': self.last_reconciliation.isoformat()
                    }
            
            # Thực hiện đối chiếu
            logger.info(f"🔄 Forced reconciliation initiated{' for user ' + user_id if user_id else ''}")
            result = await self._reconcile_orders(user_id)
            return result
            
        except Exception as e:
            logger.error(f"❌ Error in forced reconciliation: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _reconcile_orders(self, user_id: str = None):
        """Reconcile orders between database and exchange"""
        try:
            logger.info(f"🔄 Starting order reconciliation{' for user ' + user_id if user_id else ''}")
            self.last_reconciliation = datetime.now(timezone.utc)

            # Get all open orders from database
            if user_id:
                orders = self.db.get_orders(user_id=user_id)
            else:
                orders = self.db.get_orders()
                
            open_orders = [o for o in orders if o['status'] in ['open', 'partially_filled']]

            reconciled_count = 0
            for order in open_orders:
                try:
                    api_status = await self._get_order_status_from_api(
                        order['order_id'], order['symbol']
                    )
                    if api_status:
                        await self._process_api_order_update(order, api_status)
                        reconciled_count += 1
                except Exception as e:
                    logger.error(f"❌ Error reconciling order {order['order_id']}: {e}")

            logger.info(f"✅ Order reconciliation completed: {reconciled_count}/{len(open_orders)} orders checked")
            return {
                'success': True,
                'reconciled_count': reconciled_count,
                'total_orders': len(open_orders),
                'timestamp': self.last_reconciliation.isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Error in order reconciliation: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    # ==================== QUERY METHODS ====================

    def get_user_orders(self, user_id: str, status: str = None, limit: int = 50) -> List[Dict]:
        """Get orders for a specific user"""
        try:
            orders = self.db.get_orders(user_id=user_id, status=status, limit=limit)
            logger.debug(f"📋 Retrieved {len(orders)} orders for user {user_id}")
            return orders
        except Exception as e:
            logger.error(f"❌ Failed to get user orders: {e}")
            return []

    def get_pending_orders(self, user_id: str = None) -> List[Dict]:
        """Get all pending orders"""
        try:
            if user_id:
                return self.get_user_orders(user_id, status='open')
            else:
                return self.db.get_orders(status='open')
        except Exception as e:
            logger.error(f"❌ Failed to get pending orders: {e}")
            return []

    def get_order_summary(self, user_id: str) -> Dict:
        """Get order summary for user"""
        try:
            all_orders = self.get_user_orders(user_id)

            summary = {
                'total_orders': len(all_orders),
                'open': len([o for o in all_orders if o['status'] == 'open']),
                'filled': len([o for o in all_orders if o['status'] == 'filled']),
                'cancelled': len([o for o in all_orders if o['status'] == 'cancelled']),
                'rejected': len([o for o in all_orders if o['status'] == 'rejected']),
                'recent_orders': all_orders[:5]  # Last 5 orders
            }

            return summary

        except Exception as e:
            logger.error(f"❌ Failed to get order summary: {e}")
            return {}

    def get_status(self) -> Dict[str, Any]:
        """Get tracker status"""
        return {
            'websocket_connected': self.websocket_connected,
            'backup_polling_enabled': self.backup_polling_enabled,
            'pending_orders_count': len(self.pending_orders),
            'tracked_symbols_count': len(self.tracked_symbols),
            'last_reconciliation': self.last_reconciliation.isoformat() if self.last_reconciliation else None,
            'backup_polling_interval': self.backup_polling_interval,
            'reconciliation_interval': self.reconciliation_interval
        }

    # ==================== CLEANUP METHODS ====================

    async def stop(self):
        """Stop all tracking activities"""
        try:
            # Stop backup polling
            await self._stop_backup_polling()

            # Stop reconciliation
            if self.reconciliation_task:
                self.reconciliation_task.cancel()
                try:
                    await self.reconciliation_task
                except asyncio.CancelledError:
                    pass

            logger.info("🛑 Order Tracker stopped")

        except Exception as e:
            logger.error(f"❌ Error stopping tracker: {e}")

# Global order tracker instance
order_tracker = OrderTracker()
