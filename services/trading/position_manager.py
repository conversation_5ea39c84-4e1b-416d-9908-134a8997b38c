#!/usr/bin/env python3
"""
Enhanced Position Management System for Binance Futures Trading Bot

Responsibilities:
- Real-time position tracking and P&L calculations
- Position lifecycle management (open, update, close)
- Integration with unified_order_tracker for fill updates
- Performance metrics and analytics
- Accurate margin tracking and exposure analysis
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal, ROUND_HALF_UP
from services.data.database import trading_db
from services.data.cache_service import get_cache

logger = logging.getLogger(__name__)

class PositionManager:
    """
    Enhanced Position Manager with comprehensive tracking capabilities
    """

    def __init__(self, trading_service=None):
        self.trading_service = trading_service
        self.db = trading_db

        # Caching for performance
        self.position_cache = get_cache("positions", max_size=100, ttl=30)
        self.pnl_cache = get_cache("pnl_calculations", max_size=50, ttl=60)

        # Performance tracking
        self.last_update = None
        self.update_count = 0

        logger.info("📊 Enhanced Position Manager initialized")

    def update_position_from_fill(self, order_data: Dict, fill_data: Dict) -> bool:
        """
        Update position when an order is filled

        Args:
            order_data: Order information from database
            fill_data: Fill information (price, amount, etc.)
        """
        try:
            user_id = order_data['user_id']
            symbol = order_data['symbol']
            side = order_data['side']
            position_side = order_data['position_side']
            fill_amount = fill_data['amount']
            fill_price = fill_data['price']

            logger.info(f"🔄 Updating position: {symbol} {position_side} {fill_amount} @ {fill_price}")

            # Get existing position
            existing_position = self.get_position(user_id, symbol, position_side)

            if existing_position:
                # Update existing position
                success = self._update_existing_position(
                    existing_position, side, fill_amount, fill_price, order_data
                )
            else:
                # Create new position
                success = self._create_new_position(
                    user_id, symbol, position_side, side, fill_amount, fill_price, order_data
                )

            if success:
                logger.info(f"✅ Position updated successfully: {symbol} {position_side}")
                return True
            else:
                logger.error(f"❌ Failed to update position: {symbol} {position_side}")
                return False

        except Exception as e:
            logger.error(f"❌ Error updating position from fill: {e}")
            return False

    def _create_new_position(self, user_id: str, symbol: str, position_side: str,
                           side: str, amount: float, price: float, order_data: Dict) -> bool:
        """Create a new position"""
        try:
            position_id = f"pos_{symbol}_{position_side}_{user_id}_{int(datetime.now().timestamp())}"

            # Determine position amount based on side
            if (side == 'buy' and position_side == 'LONG') or (side == 'sell' and position_side == 'SHORT'):
                # Opening position
                position_amount = amount
            else:
                # This shouldn't happen for new positions, but handle it
                logger.warning(f"Unexpected side {side} for new {position_side} position")
                position_amount = amount if side == 'buy' else -amount

            position_data = {
                'position_id': position_id,
                'user_id': user_id,
                'symbol': symbol,
                'side': side,
                'position_side': position_side,
                'amount': position_amount,
                'entry_price': price,
                'current_price': price,
                'unrealized_pnl': 0.0,
                'realized_pnl': 0.0,
                'margin_used': self._calculate_margin_used(symbol, position_amount, price),
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat(),
                'orders': [order_data['order_id']],
                'status': 'open'
            }

            success = self._save_position(position_data)
            if success:
                logger.info(f"✅ New position created: {position_id}")

            return success

        except Exception as e:
            logger.error(f"❌ Error creating new position: {e}")
            return False

    def _update_existing_position(self, position: Dict, side: str,
                                fill_amount: float, fill_price: float, order_data: Dict) -> bool:
        """Update existing position with new fill"""
        try:
            current_amount = position['amount']
            current_entry = position['entry_price']
            position_side = position['position_side']

            if (side == 'buy' and position_side == 'LONG') or (side == 'sell' and position_side == 'SHORT'):
                # Adding to position
                new_amount = current_amount + fill_amount

                # Calculate new weighted average entry price
                total_cost = (current_amount * current_entry) + (fill_amount * fill_price)
                new_entry_price = total_cost / new_amount if new_amount != 0 else current_entry

                logger.info(f"📈 Adding to position: {current_amount} + {fill_amount} = {new_amount}")

            else:
                # Reducing position (partial/full close)
                # Ensure we don't close more than we have
                actual_close_amount = min(fill_amount, current_amount)
                new_amount = current_amount - actual_close_amount
                new_entry_price = current_entry  # Entry price doesn't change when closing

                # Calculate realized P&L for the closed portion
                if position_side == 'LONG':
                    realized_pnl = (fill_price - current_entry) * actual_close_amount
                else:  # SHORT
                    realized_pnl = (current_entry - fill_price) * actual_close_amount

                # Update total realized P&L
                total_realized_pnl = position.get('realized_pnl', 0.0) + realized_pnl

                logger.info(f"📉 Reducing position: {current_amount} - {actual_close_amount} = {new_amount}")
                logger.info(f"💰 Realized P&L: ${realized_pnl:.2f}")

                if actual_close_amount < fill_amount:
                    logger.warning(f"⚠️ Attempted to close {fill_amount} but only {actual_close_amount} available")

                # Update position with realized P&L
                position['realized_pnl'] = total_realized_pnl

            # Update position data
            position['amount'] = new_amount
            position['entry_price'] = new_entry_price
            position['updated_at'] = datetime.now(timezone.utc).isoformat()

            # Add order ID to orders list if not already present
            if 'orders' not in position:
                position['orders'] = []

            current_order_id = order_data.get('order_id')
            if current_order_id and current_order_id not in position['orders']:
                position['orders'].append(current_order_id)

            # Close position if amount is zero or very small
            if abs(new_amount) < 0.000001:
                position['status'] = 'closed'
                position['amount'] = 0.0
                logger.info(f"🔒 Position closed: {position['symbol']} {position['position_side']}")

            # Update margin used
            position['margin_used'] = self._calculate_margin_used(
                position['symbol'], position['amount'], position['entry_price']
            )

            # Save updated position
            success = self._update_position_in_db(position)

            return success

        except Exception as e:
            logger.error(f"❌ Error updating existing position: {e}")
            return False

    def calculate_unrealized_pnl(self, position: Dict, current_price: float) -> float:
        """Calculate unrealized P&L for a position"""
        try:
            amount = position['amount']
            entry_price = position['entry_price']
            position_side = position['position_side']

            if amount == 0:
                return 0.0

            if position_side == 'LONG':
                unrealized_pnl = (current_price - entry_price) * amount
            else:  # SHORT
                unrealized_pnl = (entry_price - current_price) * amount

            return unrealized_pnl

        except Exception as e:
            logger.error(f"❌ Error calculating unrealized P&L: {e}")
            return 0.0

    def update_position_price(self, user_id: str, symbol: str, current_price: float) -> bool:
        """Update position with current market price and recalculate P&L"""
        try:
            positions = self.get_user_positions(user_id, symbol=symbol, status='open')

            for position in positions:
                # Calculate new unrealized P&L
                unrealized_pnl = self.calculate_unrealized_pnl(position, current_price)

                # Update position
                position['current_price'] = current_price
                position['unrealized_pnl'] = unrealized_pnl
                position['updated_at'] = datetime.now(timezone.utc).isoformat()

                # Save to database
                self._update_position_in_db(position)

                logger.debug(f"📊 Updated {symbol} price: ${current_price:.2f}, P&L: ${unrealized_pnl:.2f}")

            return True

        except Exception as e:
            logger.error(f"❌ Error updating position price: {e}")
            return False

    def get_position(self, user_id: str, symbol: str, position_side: str,
                    include_closed: bool = False) -> Optional[Dict]:
        """Get specific position"""
        try:
            with self.db.get_connection() as conn:
                if include_closed:
                    query = """
                        SELECT * FROM positions
                        WHERE user_id = ? AND symbol = ? AND position_side = ?
                        ORDER BY created_at DESC LIMIT 1
                    """
                else:
                    query = """
                        SELECT * FROM positions
                        WHERE user_id = ? AND symbol = ? AND position_side = ? AND status = 'open'
                        ORDER BY created_at DESC LIMIT 1
                    """

                row = conn.execute(query, (user_id, symbol, position_side)).fetchone()

                if row:
                    position = dict(row)
                    # Parse orders JSON
                    if position.get('orders'):
                        import json
                        try:
                            position['orders'] = json.loads(position['orders'])
                        except:
                            position['orders'] = []
                    else:
                        position['orders'] = []

                    return position

                return None

        except Exception as e:
            logger.error(f"❌ Error getting position: {e}")
            return None

    def get_user_positions(self, user_id: str, symbol: str = None,
                          status: str = None) -> List[Dict]:
        """Get all positions for a user"""
        try:
            query = "SELECT * FROM positions WHERE user_id = ?"
            params = [user_id]

            if symbol:
                query += " AND symbol = ?"
                params.append(symbol)

            if status:
                query += " AND status = ?"
                params.append(status)

            query += " ORDER BY updated_at DESC"

            with self.db.get_connection() as conn:
                rows = conn.execute(query, params).fetchall()

                positions = []
                for row in rows:
                    position = dict(row)
                    # Parse orders JSON
                    if position.get('orders'):
                        import json
                        try:
                            position['orders'] = json.loads(position['orders'])
                        except:
                            position['orders'] = []
                    else:
                        position['orders'] = []

                    positions.append(position)

                return positions

        except Exception as e:
            logger.error(f"❌ Error getting user positions: {e}")
            return []

    def _calculate_margin_used(self, symbol: str, amount: float, price: float) -> float:
        """Calculate margin used for position (simplified)"""
        try:
            # Simplified calculation - in reality this would depend on leverage
            # Assuming 10x leverage for futures
            leverage = 10.0
            notional_value = abs(amount) * price
            margin_used = notional_value / leverage

            return margin_used

        except Exception as e:
            logger.error(f"❌ Error calculating margin: {e}")
            return 0.0

    def _save_position(self, position_data: Dict) -> bool:
        """Save position to database"""
        try:
            import json

            with self.db.get_connection() as conn:
                conn.execute("""
                    INSERT INTO positions (
                        position_id, user_id, symbol, side, position_side, amount, entry_price,
                        current_price, unrealized_pnl, realized_pnl, margin_used,
                        created_at, updated_at, orders, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    position_data['position_id'],
                    position_data['user_id'],
                    position_data['symbol'],
                    position_data['side'],
                    position_data['position_side'],
                    position_data['amount'],
                    position_data['entry_price'],
                    position_data['current_price'],
                    position_data['unrealized_pnl'],
                    position_data['realized_pnl'],
                    position_data['margin_used'],
                    position_data['created_at'],
                    position_data['updated_at'],
                    json.dumps(position_data['orders']),
                    position_data['status']
                ))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"❌ Error saving position: {e}")
            return False

    def _update_position_in_db(self, position: Dict) -> bool:
        """Update position in database"""
        try:
            import json

            with self.db.get_connection() as conn:
                conn.execute("""
                    UPDATE positions SET
                        amount = ?, entry_price = ?, current_price = ?,
                        unrealized_pnl = ?, realized_pnl = ?, margin_used = ?,
                        updated_at = ?, orders = ?, status = ?
                    WHERE position_id = ?
                """, (
                    position['amount'],
                    position['entry_price'],
                    position['current_price'],
                    position['unrealized_pnl'],
                    position['realized_pnl'],
                    position['margin_used'],
                    position['updated_at'],
                    json.dumps(position['orders']),
                    position['status'],
                    position['position_id']
                ))
                conn.commit()

            return True

        except Exception as e:
            logger.error(f"❌ Error updating position in database: {e}")
            return False

    # ==================== ENHANCED POSITION ANALYTICS ====================

    def get_position_analytics(self, user_id: str, days: int = 30) -> Dict:
        """Get comprehensive position analytics for user"""
        try:
            cache_key = f"analytics_{user_id}_{days}"
            cached = self.pnl_cache.get(cache_key)
            if cached:
                return cached

            # Get positions from last N days
            start_date = (datetime.now(timezone.utc) - timedelta(days=days)).isoformat()

            with self.db.get_connection() as conn:
                # Get all positions for user in timeframe
                positions = conn.execute("""
                    SELECT * FROM positions
                    WHERE user_id = ? AND created_at >= ?
                    ORDER BY created_at DESC
                """, (user_id, start_date)).fetchall()

                # Calculate analytics
                total_positions = len(positions)
                profitable_positions = len([p for p in positions if float(p.get('realized_pnl', 0)) > 0])
                total_pnl = sum(float(p.get('realized_pnl', 0)) for p in positions)
                total_volume = sum(abs(float(p.get('amount', 0)) * float(p.get('entry_price', 0))) for p in positions)

                analytics = {
                    'total_positions': total_positions,
                    'profitable_positions': profitable_positions,
                    'win_rate': (profitable_positions / total_positions * 100) if total_positions > 0 else 0,
                    'total_pnl': total_pnl,
                    'total_volume': total_volume,
                    'average_pnl': total_pnl / total_positions if total_positions > 0 else 0,
                    'largest_win': max([float(p.get('realized_pnl', 0)) for p in positions], default=0),
                    'largest_loss': min([float(p.get('realized_pnl', 0)) for p in positions], default=0),
                    'timeframe_days': days,
                    'calculated_at': datetime.now(timezone.utc).isoformat()
                }

                self.pnl_cache.set(cache_key, analytics)
                return analytics

        except Exception as e:
            logger.error(f"❌ Error calculating position analytics: {e}")
            return {}

    def get_real_time_pnl(self, user_id: str) -> Dict:
        """Get real-time P&L for all open positions"""
        try:
            open_positions = self.get_user_positions(user_id, status='open')

            if not open_positions:
                return {
                    'total_unrealized_pnl': 0.0,
                    'total_realized_pnl': 0.0,
                    'position_count': 0,
                    'positions': []
                }

            total_unrealized = 0.0
            total_realized = 0.0
            position_details = []

            for position in open_positions:
                unrealized_pnl = float(position.get('unrealized_pnl', 0))
                realized_pnl = float(position.get('realized_pnl', 0))

                total_unrealized += unrealized_pnl
                total_realized += realized_pnl

                position_details.append({
                    'symbol': position['symbol'],
                    'side': position['side'],
                    'amount': position['amount'],
                    'entry_price': position['entry_price'],
                    'current_price': position.get('current_price', position['entry_price']),
                    'unrealized_pnl': unrealized_pnl,
                    'realized_pnl': realized_pnl,
                    'margin_used': position.get('margin_used', 0)
                })

            return {
                'total_unrealized_pnl': total_unrealized,
                'total_realized_pnl': total_realized,
                'total_pnl': total_unrealized + total_realized,
                'position_count': len(open_positions),
                'positions': position_details,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Error calculating real-time P&L: {e}")
            return {}

    def update_positions_with_market_data(self, price_updates: Dict[str, float]) -> int:
        """Update multiple positions with current market prices"""
        try:
            updated_count = 0

            for symbol, current_price in price_updates.items():
                # Get all open positions for this symbol
                with self.db.get_connection() as conn:
                    positions = conn.execute("""
                        SELECT * FROM positions
                        WHERE symbol = ? AND status = 'open'
                    """, (symbol,)).fetchall()

                    for position in positions:
                        # Update position with current price
                        success = self.update_position_price(
                            position['user_id'],
                            symbol,
                            current_price
                        )
                        if success:
                            updated_count += 1

            self.last_update = datetime.now(timezone.utc)
            self.update_count += updated_count

            if updated_count > 0:
                logger.debug(f"📊 Updated {updated_count} positions with market data")

            return updated_count

        except Exception as e:
            logger.error(f"❌ Error updating positions with market data: {e}")
            return 0

    def get_performance_metrics(self) -> Dict:
        """Get position manager performance metrics"""
        return {
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'total_updates': self.update_count,
            'cache_stats': {
                'position_cache_size': len(self.position_cache._cache) if hasattr(self.position_cache, '_cache') else 0,
                'pnl_cache_size': len(self.pnl_cache._cache) if hasattr(self.pnl_cache, '_cache') else 0
            }
        }

# Global position manager instance
position_manager = PositionManager()
