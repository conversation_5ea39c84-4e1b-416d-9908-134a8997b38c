#!/usr/bin/env python3
"""
Streamlined Notification Service for Trading Bot

Responsibilities:
- Discord-specific notifications with rich embeds
- Order fill alerts and position updates
- Risk alerts and margin warnings
- Trading status dashboard updates
- Real-time notification delivery

Does NOT handle:
- Order tracking (handled by unified_order_tracker)
- Position calculations (handled by position_manager)
- Risk calculations (handled by risk_manager)
"""

import logging
import discord
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from services.data.database import trading_db
from services.data.cache_service import get_cache

logger = logging.getLogger(__name__)

class NotificationService:
    """
    Streamlined notification service focused on Discord delivery
    """

    def __init__(self, bot=None):
        self.bot = bot
        self.db = trading_db

        # Notification caching and rate limiting
        self.notification_cache = get_cache("notifications", max_size=100, ttl=300)
        self.rate_limit_cache = get_cache("rate_limits", max_size=50, ttl=60)

        # Notification statistics
        self.stats = {
            'total_sent': 0,
            'failed_sends': 0,
            'rate_limited': 0,
            'last_notification': None
        }

        logger.info("🔔 Streamlined Notification Service initialized")

    def set_bot(self, bot):
        """Set Discord bot instance"""
        self.bot = bot

    async def send_order_notification(self, user_id: str, notification_type: str,
                                    order_data: Dict, channel_id: str = None) -> bool:
        """
        Send order-related notifications

        Args:
            user_id: Discord user ID
            notification_type: 'order_placed', 'order_filled', 'order_cancelled'
            order_data: Order information
            channel_id: Target channel ID (optional)
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set")
                return False

            embed = self._create_order_embed(notification_type, order_data)

            # Determine where to send notification
            target_channel = None
            if channel_id:
                target_channel = self.bot.get_channel(int(channel_id))

            if not target_channel:
                # Try to send DM to user
                try:
                    user = await self.bot.fetch_user(int(user_id))
                    target_channel = user
                except:
                    logger.error(f"Could not find user or channel for notification")
                    return False

            # Send notification
            await target_channel.send(embed=embed)

            # Log notification to database
            self.db.log_notification(
                user_id=user_id,
                notification_type=notification_type,
                title=embed.title,
                message=embed.description or "",
                data=order_data,
                channel_id=channel_id
            )

            logger.info(f"✅ Notification sent: {notification_type} for order {order_data.get('order_id')}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to send notification: {e}")
            return False

    def _create_order_embed(self, notification_type: str, order_data: Dict) -> discord.Embed:
        """Create Discord embed for order notifications"""

        if notification_type == 'order_placed':
            embed = discord.Embed(
                title="📝 Order Placed",
                color=0x3498db,
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Symbol",
                value=order_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=order_data.get('side', 'N/A').upper(),
                inline=True
            )
            embed.add_field(
                name="Type",
                value=order_data.get('type', 'N/A').upper(),
                inline=True
            )
            embed.add_field(
                name="Amount",
                value=f"{order_data.get('amount', 0):.6f}",
                inline=True
            )
            embed.add_field(
                name="Price",
                value=f"${order_data.get('price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Position",
                value=order_data.get('position_side', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Order ID",
                value=f"`{order_data.get('order_id', 'N/A')}`",
                inline=False
            )

        elif notification_type == 'order_filled':
            embed = discord.Embed(
                title="✅ Order Filled",
                color=0x00ff88,
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Symbol",
                value=order_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=order_data.get('side', 'N/A').upper(),
                inline=True
            )
            embed.add_field(
                name="Amount",
                value=f"{order_data.get('filled_amount', order_data.get('amount', 0)):.6f}",
                inline=True
            )
            embed.add_field(
                name="Fill Price",
                value=f"${order_data.get('filled_price', order_data.get('price', 0)):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Value",
                value=f"${(order_data.get('filled_amount', 0) * order_data.get('filled_price', 0)):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Position",
                value=order_data.get('position_side', 'N/A'),
                inline=True
            )

            # Add P&L if available (will be calculated in Phase 2)
            if order_data.get('pnl'):
                pnl = order_data['pnl']
                pnl_color = "🟢" if pnl >= 0 else "🔴"
                embed.add_field(
                    name="P&L",
                    value=f"{pnl_color} ${pnl:,.2f}",
                    inline=True
                )

        elif notification_type == 'order_cancelled':
            embed = discord.Embed(
                title="❌ Order Cancelled",
                color=0xff4444,
                timestamp=datetime.utcnow()
            )

            embed.add_field(
                name="Symbol",
                value=order_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=order_data.get('side', 'N/A').upper(),
                inline=True
            )
            embed.add_field(
                name="Amount",
                value=f"{order_data.get('amount', 0):.6f}",
                inline=True
            )
            embed.add_field(
                name="Price",
                value=f"${order_data.get('price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Reason",
                value=order_data.get('cancel_reason', 'Manual cancellation'),
                inline=False
            )

        else:
            # Generic notification
            embed = discord.Embed(
                title="📊 Order Update",
                color=0xffa500,
                timestamp=datetime.utcnow()
            )
            embed.add_field(
                name="Order ID",
                value=f"`{order_data.get('order_id', 'N/A')}`",
                inline=False
            )
            embed.add_field(
                name="Status",
                value=order_data.get('status', 'Unknown'),
                inline=True
            )

        embed.set_footer(text="Binance Futures Trading Bot")
        return embed

    def _create_position_embed(self, notification_type: str, position_data: Dict) -> discord.Embed:
        """Create Discord embed for position notifications"""

        if notification_type == 'position_opened':
            embed = discord.Embed(
                title="📈 Position Opened",
                color=0x00ff88,
                timestamp=datetime.now()
            )

            embed.add_field(
                name="Symbol",
                value=position_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=position_data.get('side', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Size",
                value=f"{position_data.get('size', 0):.6f}",
                inline=True
            )
            embed.add_field(
                name="Entry Price",
                value=f"${position_data.get('entry_price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Margin Used",
                value=f"${position_data.get('margin_used', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Position ID",
                value=f"`{position_data.get('position_id', 'N/A')}`",
                inline=False
            )

        elif notification_type == 'position_updated':
            embed = discord.Embed(
                title="📊 Position Updated",
                color=0x3498db,
                timestamp=datetime.now()
            )

            pnl = position_data.get('unrealized_pnl', 0)
            pnl_color = "🟢" if pnl >= 0 else "🔴"
            pnl_percent = (pnl / (position_data.get('size', 1) * position_data.get('entry_price', 1))) * 100

            embed.add_field(
                name="Symbol",
                value=position_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=position_data.get('side', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Size",
                value=f"{position_data.get('size', 0):.6f}",
                inline=True
            )
            embed.add_field(
                name="Entry Price",
                value=f"${position_data.get('entry_price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Current Price",
                value=f"${position_data.get('current_price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Unrealized P&L",
                value=f"{pnl_color} ${pnl:,.2f} ({pnl_percent:+.2f}%)",
                inline=True
            )

        elif notification_type == 'position_closed':
            embed = discord.Embed(
                title="🔒 Position Closed",
                color=0xff6b35,
                timestamp=datetime.now()
            )

            realized_pnl = position_data.get('realized_pnl', 0)
            pnl_color = "🟢" if realized_pnl >= 0 else "🔴"

            embed.add_field(
                name="Symbol",
                value=position_data.get('symbol', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Side",
                value=position_data.get('side', 'N/A'),
                inline=True
            )
            embed.add_field(
                name="Size",
                value=f"{position_data.get('size', 0):.6f}",
                inline=True
            )
            embed.add_field(
                name="Entry Price",
                value=f"${position_data.get('entry_price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Exit Price",
                value=f"${position_data.get('exit_price', 0):,.2f}",
                inline=True
            )
            embed.add_field(
                name="Realized P&L",
                value=f"{pnl_color} ${realized_pnl:,.2f}",
                inline=True
            )

        else:
            # Generic position notification
            embed = discord.Embed(
                title="📊 Position Update",
                color=0xffa500,
                timestamp=datetime.now()
            )
            embed.add_field(
                name="Position ID",
                value=f"`{position_data.get('position_id', 'N/A')}`",
                inline=False
            )
            embed.add_field(
                name="Status",
                value=position_data.get('status', 'Unknown'),
                inline=True
            )

        embed.set_footer(text="Position Management System")
        return embed

    async def send_position_notification(self, user_id: str, notification_type: str,
                                       position_data: Dict, channel_id: str = None) -> bool:
        """Send position-related notifications"""
        try:
            if not self.bot:
                logger.error("Bot instance not set")
                return False

            embed = self._create_position_embed(notification_type, position_data)

            # Determine where to send notification
            target_channel = None
            if channel_id:
                target_channel = self.bot.get_channel(int(channel_id))

            if not target_channel:
                # Try to send DM to user
                try:
                    user = await self.bot.fetch_user(int(user_id))
                    target_channel = user
                except:
                    logger.error(f"Could not find user or channel for position notification")
                    return False

            # Send notification
            await target_channel.send(embed=embed)

            # Log notification to database
            self.db.log_notification(
                user_id=user_id,
                notification_type=notification_type,
                title=embed.title,
                message=embed.description or "",
                data=position_data,
                channel_id=channel_id
            )

            logger.info(f"✅ Position notification sent: {notification_type}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to send position notification: {e}")
            return False

    async def send_alert(self, user_id: str, alert_type: str, message: str,
                        data: Dict = None, channel_id: str = None) -> bool:
        """Send alert notifications"""
        try:
            if not self.bot:
                return False

            embed = discord.Embed(
                title=f"⚠️ {alert_type.replace('_', ' ').title()}",
                description=message,
                color=0xff6b35,
                timestamp=datetime.utcnow()
            )

            if data:
                for key, value in data.items():
                    embed.add_field(name=key.title(), value=str(value), inline=True)

            embed.set_footer(text="Trading Alert System")

            # Send to channel or DM
            target_channel = None
            if channel_id:
                target_channel = self.bot.get_channel(int(channel_id))

            if not target_channel:
                try:
                    user = await self.bot.fetch_user(int(user_id))
                    target_channel = user
                except:
                    return False

            await target_channel.send(embed=embed)

            # Log alert
            self.db.log_notification(
                user_id=user_id,
                notification_type=alert_type,
                title=embed.title,
                message=message,
                data=data,
                channel_id=channel_id
            )

            logger.info(f"✅ Alert sent: {alert_type}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to send alert: {e}")
            return False

    def get_notification_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """Get notification history for user"""
        try:
            with self.db.get_connection() as conn:
                rows = conn.execute("""
                    SELECT * FROM notifications
                    WHERE user_id = ?
                    ORDER BY sent_at DESC
                    LIMIT ?
                """, (user_id, limit)).fetchall()

                notifications = []
                for row in rows:
                    notif = dict(row)
                    # Parse JSON data if exists
                    if notif.get('data'):
                        try:
                            import json
                            notif['data'] = json.loads(notif['data'])
                        except:
                            pass
                    notifications.append(notif)

                return notifications
        except Exception as e:
            logger.error(f"❌ Failed to get notification history: {e}")
            return []

# Global notification service instance
notification_service = NotificationService()
