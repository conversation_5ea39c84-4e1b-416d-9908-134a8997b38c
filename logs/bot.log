2025-05-28 17:44:53,714 - important - INFO - Logging system initialized
2025-05-28 17:44:53,714 - important - INFO - Log level set to: INFO
2025-05-28 17:44:55,336 - important - INFO - Logging system initialized
2025-05-28 17:44:55,336 - important - INFO - Log level set to: INFO
2025-05-28 17:44:55,336 - important - INFO - Starting Discord bot initialization...
2025-05-28 17:44:55,345 - important - INFO - ✅ Discord token loaded successfully
2025-05-28 17:44:55,345 - important - INFO - Token length: 72 characters
2025-05-28 17:44:55,347 - services.data.database_service - INFO - Database initialized successfully
2025-05-28 17:44:55,348 - services.market.market_service - INFO - Loaded volatility thresholds from config: {'15m': [5, 8, 12], '1h': [8, 12, 20], 'daily': [5, 8, 12]}
2025-05-28 17:44:55,356 - services.discord.discord_bot_logging - INFO - DiscordBotLoggingService initialized
2025-05-28 17:44:55,357 - services.market.news_service - INFO - News Service initialized
2025-05-28 17:44:55,357 - handlers.discord.alerts.market_news_handler - INFO - Market News Handler initialized
2025-05-28 17:44:55,358 - important - INFO - ✅ Bot instance created
2025-05-28 17:44:55,358 - important - INFO - Bot intents: 53608189
2025-05-28 17:44:55,358 - important - INFO - Bot command prefix: !
2025-05-28 17:44:55,358 - important - INFO - Bot guild ID from config: 1375879723296489562
2025-05-28 17:44:55,358 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 1/3)
2025-05-28 17:44:55,359 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:44:55,359 - important - INFO - ✅ Discord logging service initialized
2025-05-28 17:44:55,360 - important - INFO - ✅ Signal handlers registered
2025-05-28 17:44:55,360 - important - INFO - 🚀 Starting bot connection to Discord...
2025-05-28 17:44:55,360 - discord.client - INFO - logging in using static token
2025-05-28 17:44:56,286 - services.market.chart_service - INFO - ChartService initialized
2025-05-28 17:44:56,293 - services.data.database - INFO - ✅ Database initialized successfully
2025-05-28 17:44:56,294 - services.trading.order_tracker - INFO - 🔄 Unified Order Tracker initialized
2025-05-28 17:44:56,297 - services.trading.notification_service - INFO - 🔔 Streamlined Notification Service initialized
2025-05-28 17:44:56,299 - services.trading.realtime_monitor - INFO - 🌐 Realtime Monitor initialized
2025-05-28 17:44:56,304 - services.trading.trading_service - INFO - Initializing Binance exchange with testnet: False
2025-05-28 17:44:56,304 - services.trading.trading_service - INFO - API Key length: 64
2025-05-28 17:44:56,305 - services.trading.trading_service - INFO - API Secret length: 64
2025-05-28 17:44:56,319 - services.trading.trading_service - INFO - Loading markets...
2025-05-28 17:44:59,580 - services.trading.trading_service - INFO - Binance Futures initialized successfully (testnet: False)
2025-05-28 17:44:59,580 - services.trading.trading_service - INFO - Available markets: 3691
2025-05-28 17:44:59,581 - services.trading.realtime_monitor - INFO - 📝 Registered order handler: handle_websocket_order_update
2025-05-28 17:44:59,581 - services.trading.realtime_monitor - INFO - 🚀 Starting Binance real-time monitoring...
2025-05-28 17:44:59,890 - services.trading.realtime_monitor - INFO - ✅ Listen key obtained successfully
2025-05-28 17:44:59,891 - services.trading.realtime_monitor - INFO - 🔌 Connecting to User Data Stream...
2025-05-28 17:45:00,292 - services.trading.realtime_monitor - INFO - ✅ User Data Stream connected
2025-05-28 17:45:00,293 - services.trading.order_tracker - INFO - 🔌 WebSocket status: Connected
2025-05-28 17:46:34,270 - important - INFO - Received SIGINT, initiating graceful shutdown...
2025-05-28 17:46:34,272 - important - INFO - Shutting down Discord bot...
2025-05-28 17:46:34,273 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:46:34,273 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 2/3)
2025-05-28 17:46:34,273 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:46:34,273 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:46:34,273 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:46:34,274 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:46:34,375 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:46:34,881 - important - INFO - Discord bot shutdown complete
2025-05-28 17:47:09,341 - important - INFO - Logging system initialized
2025-05-28 17:47:09,342 - important - INFO - Log level set to: INFO
2025-05-28 17:47:09,342 - important - INFO - Logging system initialized
2025-05-28 17:47:09,342 - important - INFO - Log level set to: INFO
2025-05-28 17:47:09,349 - important - INFO - 🚀 Starting Discord connection tests...
2025-05-28 17:47:09,350 - important - INFO - ✅ Token found (length: 72)
2025-05-28 17:47:09,350 - important - INFO - ✅ Bot instance created
2025-05-28 17:47:09,351 - important - INFO - Connecting to Discord...
2025-05-28 17:47:09,351 - discord.client - INFO - logging in using static token
2025-05-28 17:47:10,810 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: b712441066b75c9a4de3a1be593cd05b).
2025-05-28 17:47:12,815 - important - INFO - ✅ Bot connected as Pl#0107 (ID: 1375880115451334676)
2025-05-28 17:47:12,815 - important - INFO - Looking for guild with ID: 1375879723296489562
2025-05-28 17:47:12,815 - important - INFO - ✅ Found guild: pl's server
2025-05-28 17:47:12,815 - important - INFO - ✅ Bot is a member of the guild
2025-05-28 17:47:12,816 - important - INFO - Bot permissions:
2025-05-28 17:47:12,816 - important - INFO - - Send Messages: ✅
2025-05-28 17:47:12,816 - important - INFO - - Read Messages: ✅
2025-05-28 17:47:12,816 - important - INFO - - Manage Channels: ✅
2025-05-28 17:47:12,817 - important - INFO - 
Checking channels:
2025-05-28 17:47:12,817 - important - INFO - Channel #📢-announcements:
2025-05-28 17:47:12,817 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,817 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,818 - important - INFO - Channel #💬-general:
2025-05-28 17:47:12,818 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,819 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,819 - important - INFO - Channel #📝-bot-logs:
2025-05-28 17:47:12,820 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,820 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,821 - important - INFO - Channel #🚨-alerts:
2025-05-28 17:47:12,821 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,823 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,824 - important - INFO - Channel #📈-market-data:
2025-05-28 17:47:12,825 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,825 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,825 - important - INFO - Channel #💼-portfolio:
2025-05-28 17:47:12,826 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,826 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,827 - important - INFO - Channel #market-news:
2025-05-28 17:47:12,827 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,828 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,828 - important - INFO - Channel #economic-calendar:
2025-05-28 17:47:12,829 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,829 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,830 - important - INFO - Channel #time-trade:
2025-05-28 17:47:12,830 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,830 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,831 - important - INFO - Channel #trade:
2025-05-28 17:47:12,831 - important - INFO - - Can read: ✅
2025-05-28 17:47:12,831 - important - INFO - - Can send: ✅
2025-05-28 17:47:12,831 - important - INFO - 
📊 Test Results Summary:
2025-05-28 17:47:12,832 - important - INFO - ==================================================
2025-05-28 17:47:12,832 - important - INFO - ✅ Token Valid
2025-05-28 17:47:12,832 - important - INFO - ✅ Bot Connected
2025-05-28 17:47:12,832 - important - INFO - ✅ Guild Found
2025-05-28 17:47:12,833 - important - INFO - ✅ Bot In Guild
2025-05-28 17:47:12,833 - important - INFO - ✅ Permissions Ok
2025-05-28 17:47:12,833 - important - INFO - ✅ Can Send Messages
2025-05-28 17:47:12,833 - important - INFO - ✅ Can Read Messages
2025-05-28 17:47:12,833 - important - INFO - ✅ Can Manage Channels
2025-05-28 17:47:12,833 - important - INFO - ==================================================
2025-05-28 17:47:12,834 - important - INFO - 🎉 All tests passed! Bot is properly configured.
2025-05-28 17:47:12,864 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f5f3191a0e0>, 2936421.416658165)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f5f3190e2c0>
2025-05-28 17:47:34,604 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:47:34,606 - important - INFO - Shutting down Discord bot...
2025-05-28 17:47:34,607 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:47:34,608 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 3/3)
2025-05-28 17:47:34,608 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:47:34,608 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:47:34,608 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:47:34,609 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:47:34,709 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:47:35,211 - important - INFO - Discord bot shutdown complete
2025-05-28 17:47:41,486 - important - INFO - Received SIGINT, initiating graceful shutdown...
2025-05-28 17:47:41,486 - important - INFO - Shutting down Discord bot...
2025-05-28 17:47:41,487 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:47:41,487 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:47:41,487 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:47:41,487 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:47:41,487 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:47:41,588 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:47:42,089 - important - INFO - Discord bot shutdown complete
2025-05-28 17:47:55,856 - important - INFO - Logging system initialized
2025-05-28 17:47:55,857 - important - INFO - Log level set to: INFO
2025-05-28 17:47:57,614 - important - INFO - Logging system initialized
2025-05-28 17:47:57,614 - important - INFO - Log level set to: INFO
2025-05-28 17:47:57,615 - important - INFO - Starting Discord bot initialization...
2025-05-28 17:47:57,633 - important - INFO - ✅ Discord token loaded successfully
2025-05-28 17:47:57,633 - important - INFO - Token length: 72 characters
2025-05-28 17:47:57,637 - services.data.database_service - INFO - Database initialized successfully
2025-05-28 17:47:57,637 - services.market.market_service - INFO - Loaded volatility thresholds from config: {'15m': [5, 8, 12], '1h': [8, 12, 20], 'daily': [5, 8, 12]}
2025-05-28 17:47:57,643 - services.discord.discord_bot_logging - INFO - DiscordBotLoggingService initialized
2025-05-28 17:47:57,644 - services.market.news_service - INFO - News Service initialized
2025-05-28 17:47:57,644 - handlers.discord.alerts.market_news_handler - INFO - Market News Handler initialized
2025-05-28 17:47:57,644 - important - INFO - ✅ Bot instance created
2025-05-28 17:47:57,644 - important - INFO - Bot intents: 53608189
2025-05-28 17:47:57,645 - important - INFO - Bot command prefix: !
2025-05-28 17:47:57,645 - important - INFO - Bot guild ID from config: 1375879723296489562
2025-05-28 17:47:57,645 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 1/3)
2025-05-28 17:47:57,646 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:47:57,646 - important - INFO - ✅ Discord logging service initialized
2025-05-28 17:47:57,648 - important - INFO - ✅ Signal handlers registered
2025-05-28 17:47:57,648 - important - INFO - 🚀 Starting bot connection to Discord...
2025-05-28 17:47:57,648 - discord.client - INFO - logging in using static token
2025-05-28 17:47:58,535 - services.market.chart_service - INFO - ChartService initialized
2025-05-28 17:47:58,542 - services.data.database - INFO - ✅ Database initialized successfully
2025-05-28 17:47:58,542 - services.trading.order_tracker - INFO - 🔄 Unified Order Tracker initialized
2025-05-28 17:47:58,545 - services.trading.notification_service - INFO - 🔔 Streamlined Notification Service initialized
2025-05-28 17:47:58,547 - services.trading.realtime_monitor - INFO - 🌐 Realtime Monitor initialized
2025-05-28 17:47:58,552 - services.trading.trading_service - INFO - Initializing Binance exchange with testnet: False
2025-05-28 17:47:58,552 - services.trading.trading_service - INFO - API Key length: 64
2025-05-28 17:47:58,552 - services.trading.trading_service - INFO - API Secret length: 64
2025-05-28 17:47:58,569 - services.trading.trading_service - INFO - Loading markets...
2025-05-28 17:48:01,799 - services.trading.trading_service - INFO - Binance Futures initialized successfully (testnet: False)
2025-05-28 17:48:01,799 - services.trading.trading_service - INFO - Available markets: 3691
2025-05-28 17:48:01,799 - services.trading.realtime_monitor - INFO - 📝 Registered order handler: handle_websocket_order_update
2025-05-28 17:48:01,800 - services.trading.realtime_monitor - INFO - 🚀 Starting Binance real-time monitoring...
2025-05-28 17:48:02,110 - services.trading.realtime_monitor - INFO - ✅ Listen key obtained successfully
2025-05-28 17:48:02,111 - services.trading.realtime_monitor - INFO - 🔌 Connecting to User Data Stream...
2025-05-28 17:48:02,521 - services.trading.realtime_monitor - INFO - ✅ User Data Stream connected
2025-05-28 17:48:02,522 - services.trading.order_tracker - INFO - 🔌 WebSocket status: Connected
2025-05-28 17:49:08,975 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:49:08,977 - important - INFO - Shutting down Discord bot...
2025-05-28 17:49:08,977 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:49:08,977 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:49:08,977 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:49:08,977 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:49:08,977 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:49:08,980 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:49:08,981 - important - INFO - Shutting down Discord bot...
2025-05-28 17:49:08,981 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:49:08,982 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 2/3)
2025-05-28 17:49:08,982 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:49:08,982 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:49:08,982 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:49:08,983 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:49:09,078 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:49:09,083 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:49:09,315 - important - INFO - Logging system initialized
2025-05-28 17:49:09,315 - important - INFO - Log level set to: INFO
2025-05-28 17:49:09,580 - important - INFO - Discord bot shutdown complete
2025-05-28 17:49:09,597 - important - INFO - Discord bot shutdown complete
2025-05-28 17:49:11,049 - important - INFO - Logging system initialized
2025-05-28 17:49:11,050 - important - INFO - Log level set to: INFO
2025-05-28 17:49:11,050 - important - INFO - Starting Discord bot initialization...
2025-05-28 17:49:11,059 - important - INFO - ✅ Discord token loaded successfully
2025-05-28 17:49:11,059 - important - INFO - Token length: 72 characters
2025-05-28 17:49:11,061 - services.data.database_service - INFO - Database initialized successfully
2025-05-28 17:49:11,062 - services.market.market_service - INFO - Loaded volatility thresholds from config: {'15m': [5, 8, 12], '1h': [8, 12, 20], 'daily': [5, 8, 12]}
2025-05-28 17:49:11,065 - services.discord.discord_bot_logging - INFO - DiscordBotLoggingService initialized
2025-05-28 17:49:11,065 - services.market.news_service - INFO - News Service initialized
2025-05-28 17:49:11,066 - handlers.discord.alerts.market_news_handler - INFO - Market News Handler initialized
2025-05-28 17:49:11,066 - important - INFO - ✅ Bot instance created
2025-05-28 17:49:11,066 - important - INFO - Bot intents: 53608189
2025-05-28 17:49:11,066 - important - INFO - Bot command prefix: !
2025-05-28 17:49:11,067 - important - INFO - Bot guild ID from config: 1375879723296489562
2025-05-28 17:49:11,068 - important - INFO - ✅ Signal handlers registered
2025-05-28 17:49:11,068 - important - INFO - 🚀 Starting bot connection to Discord...
2025-05-28 17:49:11,069 - discord.client - INFO - logging in using static token
2025-05-28 17:49:11,935 - services.market.chart_service - INFO - ChartService initialized
2025-05-28 17:49:11,941 - services.data.database - INFO - ✅ Database initialized successfully
2025-05-28 17:49:11,942 - services.trading.order_tracker - INFO - 🔄 Unified Order Tracker initialized
2025-05-28 17:49:11,943 - services.trading.notification_service - INFO - 🔔 Streamlined Notification Service initialized
2025-05-28 17:49:11,945 - services.trading.realtime_monitor - INFO - 🌐 Realtime Monitor initialized
2025-05-28 17:49:11,951 - services.trading.trading_service - INFO - Initializing Binance exchange with testnet: False
2025-05-28 17:49:11,951 - services.trading.trading_service - INFO - API Key length: 64
2025-05-28 17:49:11,951 - services.trading.trading_service - INFO - API Secret length: 64
2025-05-28 17:49:11,968 - services.trading.trading_service - INFO - Loading markets...
2025-05-28 17:49:15,031 - services.trading.trading_service - INFO - Binance Futures initialized successfully (testnet: False)
2025-05-28 17:49:15,031 - services.trading.trading_service - INFO - Available markets: 3691
2025-05-28 17:49:15,032 - services.trading.realtime_monitor - INFO - 📝 Registered order handler: handle_websocket_order_update
2025-05-28 17:49:15,032 - services.trading.realtime_monitor - INFO - 🚀 Starting Binance real-time monitoring...
2025-05-28 17:49:15,334 - services.trading.realtime_monitor - INFO - ✅ Listen key obtained successfully
2025-05-28 17:49:15,335 - services.trading.realtime_monitor - INFO - 🔌 Connecting to User Data Stream...
2025-05-28 17:49:15,726 - services.trading.realtime_monitor - INFO - ✅ User Data Stream connected
2025-05-28 17:49:15,727 - services.trading.order_tracker - INFO - 🔌 WebSocket status: Connected
2025-05-28 17:50:04,620 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:50:04,621 - important - INFO - Shutting down Discord bot...
2025-05-28 17:50:04,621 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:50:04,621 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 3/3)
2025-05-28 17:50:04,622 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:50:04,622 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:50:04,622 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:50:04,622 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:50:04,622 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:50:04,627 - important - INFO - Shutting down Discord bot...
2025-05-28 17:50:04,628 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:50:04,628 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:50:04,628 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:50:04,628 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:50:04,628 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:50:04,723 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:50:04,729 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:50:05,010 - important - INFO - Logging system initialized
2025-05-28 17:50:05,010 - important - INFO - Log level set to: INFO
2025-05-28 17:50:05,224 - important - INFO - Discord bot shutdown complete
2025-05-28 17:50:05,230 - important - INFO - Discord bot shutdown complete
2025-05-28 17:50:05,396 - important - INFO - Shutting down Discord bot...
2025-05-28 17:50:05,399 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:50:05,399 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 1/3)
2025-05-28 17:50:05,399 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:50:05,399 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:50:05,400 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:50:05,401 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:50:05,502 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:50:06,021 - important - INFO - Discord bot shutdown complete
2025-05-28 17:50:06,669 - important - INFO - Logging system initialized
2025-05-28 17:50:06,669 - important - INFO - Log level set to: INFO
2025-05-28 17:50:06,669 - important - INFO - Starting Discord bot initialization...
2025-05-28 17:50:06,677 - important - INFO - ✅ Discord token loaded successfully
2025-05-28 17:50:06,677 - important - INFO - Token length: 72 characters
2025-05-28 17:50:06,679 - services.data.database_service - INFO - Database initialized successfully
2025-05-28 17:50:06,680 - services.market.market_service - INFO - Loaded volatility thresholds from config: {'15m': [5, 8, 12], '1h': [8, 12, 20], 'daily': [5, 8, 12]}
2025-05-28 17:50:06,689 - services.discord.discord_bot_logging - INFO - DiscordBotLoggingService initialized
2025-05-28 17:50:06,690 - services.market.news_service - INFO - News Service initialized
2025-05-28 17:50:06,690 - handlers.discord.alerts.market_news_handler - INFO - Market News Handler initialized
2025-05-28 17:50:06,690 - important - INFO - ✅ Bot instance created
2025-05-28 17:50:06,691 - important - INFO - Bot intents: 53608189
2025-05-28 17:50:06,691 - important - INFO - Bot command prefix: !
2025-05-28 17:50:06,692 - important - INFO - Bot guild ID from config: 1375879723296489562
2025-05-28 17:50:06,692 - important - INFO - ✅ Signal handlers registered
2025-05-28 17:50:06,693 - important - INFO - 🚀 Starting bot connection to Discord...
2025-05-28 17:50:06,693 - discord.client - INFO - logging in using static token
2025-05-28 17:50:07,551 - services.market.chart_service - INFO - ChartService initialized
2025-05-28 17:50:07,559 - services.data.database - INFO - ✅ Database initialized successfully
2025-05-28 17:50:07,559 - services.trading.order_tracker - INFO - 🔄 Unified Order Tracker initialized
2025-05-28 17:50:07,560 - services.trading.notification_service - INFO - 🔔 Streamlined Notification Service initialized
2025-05-28 17:50:07,562 - services.trading.realtime_monitor - INFO - 🌐 Realtime Monitor initialized
2025-05-28 17:50:07,569 - services.trading.trading_service - INFO - Initializing Binance exchange with testnet: False
2025-05-28 17:50:07,570 - services.trading.trading_service - INFO - API Key length: 64
2025-05-28 17:50:07,570 - services.trading.trading_service - INFO - API Secret length: 64
2025-05-28 17:50:07,585 - services.trading.trading_service - INFO - Loading markets...
2025-05-28 17:50:10,710 - services.trading.trading_service - INFO - Binance Futures initialized successfully (testnet: False)
2025-05-28 17:50:10,711 - services.trading.trading_service - INFO - Available markets: 3691
2025-05-28 17:50:10,711 - services.trading.realtime_monitor - INFO - 📝 Registered order handler: handle_websocket_order_update
2025-05-28 17:50:10,711 - services.trading.realtime_monitor - INFO - 🚀 Starting Binance real-time monitoring...
2025-05-28 17:50:11,011 - services.trading.realtime_monitor - INFO - ✅ Listen key obtained successfully
2025-05-28 17:50:11,012 - services.trading.realtime_monitor - INFO - 🔌 Connecting to User Data Stream...
2025-05-28 17:50:11,415 - services.trading.realtime_monitor - INFO - ✅ User Data Stream connected
2025-05-28 17:50:11,415 - services.trading.order_tracker - INFO - 🔌 WebSocket status: Connected
2025-05-28 17:52:31,881 - important - INFO - Received SIGHUP, initiating graceful shutdown...
2025-05-28 17:52:31,882 - important - INFO - Shutting down Discord bot...
2025-05-28 17:52:31,883 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:52:31,883 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:52:31,883 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:52:31,883 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:52:31,884 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:52:31,985 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:52:32,486 - important - INFO - Discord bot shutdown complete
2025-05-28 17:53:39,809 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:53:39,815 - important - INFO - Shutting down Discord bot...
2025-05-28 17:53:39,816 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:53:39,816 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:53:39,816 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:53:39,816 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:53:39,817 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:53:39,815 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-28 17:53:39,821 - important - INFO - Shutting down Discord bot...
2025-05-28 17:53:39,822 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:53:39,823 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:53:39,823 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:53:39,823 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:53:39,823 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:53:39,917 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:53:39,924 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:53:40,204 - important - INFO - Logging system initialized
2025-05-28 17:53:40,204 - important - INFO - Log level set to: INFO
2025-05-28 17:53:40,419 - important - INFO - Discord bot shutdown complete
2025-05-28 17:53:40,426 - important - INFO - Discord bot shutdown complete
2025-05-28 17:53:40,610 - important - INFO - Shutting down Discord bot...
2025-05-28 17:53:40,613 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:53:40,614 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 2/3)
2025-05-28 17:53:40,614 - services.discord.discord_bot_logging - ERROR - Could not find guild with ID 1375879723296489562
2025-05-28 17:53:40,614 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:53:40,615 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:53:40,615 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:53:40,716 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:53:41,219 - important - INFO - Discord bot shutdown complete
2025-05-28 17:53:41,218 - important - INFO - Shutting down Discord bot...
2025-05-28 17:53:41,222 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:53:41,222 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 1/3)
2025-05-28 17:53:41,222 - services.discord.discord_bot_logging - ERROR - Error initializing bot logs channel: 'bool' object is not callable
2025-05-28 17:53:41,223 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:53:41,223 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:53:41,224 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:53:41,327 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:53:41,833 - important - INFO - Discord bot shutdown complete
2025-05-28 17:53:42,073 - important - INFO - Logging system initialized
2025-05-28 17:53:42,073 - important - INFO - Log level set to: INFO
2025-05-28 17:53:42,074 - important - INFO - Starting Discord bot initialization...
2025-05-28 17:53:42,084 - important - INFO - ✅ Discord token loaded successfully
2025-05-28 17:53:42,084 - important - INFO - Token length: 72 characters
2025-05-28 17:53:42,086 - services.data.database_service - INFO - Database initialized successfully
2025-05-28 17:53:42,087 - services.market.market_service - INFO - Loaded volatility thresholds from config: {'15m': [5, 8, 12], '1h': [8, 12, 20], 'daily': [5, 8, 12]}
2025-05-28 17:53:42,095 - services.discord.discord_bot_logging - INFO - DiscordBotLoggingService initialized
2025-05-28 17:53:42,096 - services.market.news_service - INFO - News Service initialized
2025-05-28 17:53:42,096 - handlers.discord.alerts.market_news_handler - INFO - Market News Handler initialized
2025-05-28 17:53:42,096 - important - INFO - ✅ Bot instance created
2025-05-28 17:53:42,097 - important - INFO - Bot intents: 53608189
2025-05-28 17:53:42,097 - important - INFO - Bot command prefix: !
2025-05-28 17:53:42,097 - important - INFO - Bot guild ID from config: 1375879723296489562
2025-05-28 17:53:42,098 - important - INFO - ✅ Signal handlers registered
2025-05-28 17:53:42,098 - important - INFO - 🚀 Starting bot connection to Discord...
2025-05-28 17:53:42,098 - discord.client - INFO - logging in using static token
2025-05-28 17:53:42,981 - services.market.chart_service - INFO - ChartService initialized
2025-05-28 17:53:42,989 - services.data.database - INFO - ✅ Database initialized successfully
2025-05-28 17:53:42,989 - services.trading.order_tracker - INFO - 🔄 Unified Order Tracker initialized
2025-05-28 17:53:42,991 - services.trading.notification_service - INFO - 🔔 Streamlined Notification Service initialized
2025-05-28 17:53:42,993 - services.trading.realtime_monitor - INFO - 🌐 Realtime Monitor initialized
2025-05-28 17:53:42,998 - services.trading.trading_service - INFO - Initializing Binance exchange with testnet: False
2025-05-28 17:53:42,999 - services.trading.trading_service - INFO - API Key length: 64
2025-05-28 17:53:42,999 - services.trading.trading_service - INFO - API Secret length: 64
2025-05-28 17:53:43,017 - services.trading.trading_service - INFO - Loading markets...
2025-05-28 17:53:46,099 - services.trading.trading_service - INFO - Binance Futures initialized successfully (testnet: False)
2025-05-28 17:53:46,099 - services.trading.trading_service - INFO - Available markets: 3691
2025-05-28 17:53:46,099 - services.trading.realtime_monitor - INFO - 📝 Registered order handler: handle_websocket_order_update
2025-05-28 17:53:46,099 - services.trading.realtime_monitor - INFO - 🚀 Starting Binance real-time monitoring...
2025-05-28 17:53:46,402 - services.trading.realtime_monitor - INFO - ✅ Listen key obtained successfully
2025-05-28 17:53:46,402 - services.trading.realtime_monitor - INFO - 🔌 Connecting to User Data Stream...
2025-05-28 17:53:46,784 - services.trading.realtime_monitor - INFO - ✅ User Data Stream connected
2025-05-28 17:53:46,784 - services.trading.order_tracker - INFO - 🔌 WebSocket status: Connected
2025-05-28 17:55:21,524 - important - INFO - Shutting down Discord bot...
2025-05-28 17:55:21,526 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:55:21,526 - services.discord.discord_bot_logging - INFO - Initializing Discord logging service (attempt 1/3)
2025-05-28 17:55:21,526 - services.discord.discord_bot_logging - ERROR - Error initializing bot logs channel: 'bool' object is not callable
2025-05-28 17:55:21,527 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:55:21,527 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:55:21,528 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:55:21,629 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:55:22,135 - important - INFO - Discord bot shutdown complete
2025-05-28 17:55:38,884 - important - INFO - Received SIGHUP, initiating graceful shutdown...
2025-05-28 17:55:38,886 - important - INFO - Shutting down Discord bot...
2025-05-28 17:55:38,886 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-28 17:55:38,887 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-28 17:55:38,887 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-28 17:55:38,887 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-28 17:55:38,887 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 17:55:38,988 - important - INFO - ✅ HTTP client service closed
2025-05-28 17:55:39,489 - important - INFO - Discord bot shutdown complete
2025-05-28 18:03:52,720 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 18:09:25,762 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 18:09:59,655 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 18:13:55,297 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 18:14:34,112 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-28 18:21:32,931 - services.trading.realtime_monitor - ERROR - ❌ Error pinging listen key: [Errno 32] Broken pipe
2025-05-28 18:21:33,146 - services.trading.realtime_monitor - ERROR - ❌ Error getting listen key: Server disconnected
2025-05-28 18:21:33,207 - services.trading.realtime_monitor - ERROR - ❌ Failed to get listen key
2025-05-28 18:21:43,709 - __main__ - ERROR - Error loading extensions: Extension 'handlers.discord.trading.position_control_commands' raised an error: CommandAlreadyRegistered: Command 'tp' already registered.
2025-05-28 18:22:48,727 - __main__ - WARNING - Slow command execution: watchlist took 7.71s
2025-05-28 18:24:36,430 - services.data.database - ERROR - ❌ Failed to create order: 'type'
2025-05-28 18:25:32,228 - services.trading.trading_service - ERROR - Get orders error: binance fetchOpenOrders() WARNING: fetching open orders without specifying a symbol has stricter rate limits(10 times more for spot, 40 times more for other markets) compared to requesting with symbol argument. To acknowledge self warning, set binance.options["warnOnFetchOpenOrdersWithoutSymbol"] = False to suppress self warning message.
2025-05-29 02:29:55,048 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-29 02:29:55,073 - important - INFO - Received SIGHUP, initiating graceful shutdown...
2025-05-29 02:29:55,073 - important - INFO - Shutting down Discord bot...
2025-05-29 02:29:55,109 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-29 02:29:55,110 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-29 02:29:55,110 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-29 02:29:55,110 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-29 02:29:55,119 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,124 - important - INFO - Shutting down Discord bot...
2025-05-29 02:29:55,124 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-29 02:29:55,124 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-29 02:29:55,124 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-29 02:29:55,124 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-29 02:29:55,124 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,142 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,187 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,225 - important - INFO - ✅ HTTP client service closed
2025-05-29 02:29:55,226 - important - INFO - ✅ HTTP client service closed
2025-05-29 02:29:55,121 - important - INFO - Received SIGTERM, initiating graceful shutdown...
2025-05-29 02:29:55,270 - important - INFO - Received SIGHUP, initiating graceful shutdown...
2025-05-29 02:29:55,274 - important - INFO - Shutting down Discord bot...
2025-05-29 02:29:55,297 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,319 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,444 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-29 02:29:55,447 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-29 02:29:55,447 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-29 02:29:55,447 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-29 02:29:55,465 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,472 - important - INFO - Shutting down Discord bot...
2025-05-29 02:29:55,476 - services.discord.discord_bot_logging - INFO - Bot logs channel not initialized, attempting to initialize...
2025-05-29 02:29:55,478 - services.discord.discord_bot_logging - ERROR - Max initialization attempts (3) reached
2025-05-29 02:29:55,479 - services.discord.discord_bot_logging - ERROR - Failed to initialize bot logs channel for status update (shutdown)
2025-05-29 02:29:55,479 - services.discord.discord_bot_logging - INFO - Status notification sent: shutdown - 🛑 **ChartFix Bot đang tắt...**
2025-05-29 02:29:55,479 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,575 - important - INFO - ✅ HTTP client service closed
2025-05-29 02:29:55,599 - important - INFO - ✅ HTTP client service closed
2025-05-29 02:29:55,589 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:29:55,727 - important - INFO - Discord bot shutdown complete
2025-05-29 02:29:55,727 - important - INFO - Discord bot shutdown complete
2025-05-29 02:29:56,101 - important - INFO - Discord bot shutdown complete
2025-05-29 02:29:56,101 - important - INFO - Discord bot shutdown complete
2025-05-29 02:34:42,648 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:34:42,649 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
2025-05-29 02:34:44,064 - __main__ - ERROR - Error stopping economic calendar service: 'CryptoBot' object has no attribute 'economic_calendar_service'
